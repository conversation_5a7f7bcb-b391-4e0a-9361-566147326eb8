<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面导航 - 民航招飞管理系统原型</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
        }
        
        .title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .subtitle {
            font-size: 18px;
            opacity: 0.9;
        }
        
        .page-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }
        
        .page-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        }
        
        .page-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 24px;
            color: white;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 10px;
        }
        
        .page-desc {
            color: #606266;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .page-features {
            list-style: none;
            margin-bottom: 25px;
        }
        
        .page-features li {
            color: #909399;
            font-size: 14px;
            margin-bottom: 5px;
            padding-left: 15px;
            position: relative;
        }
        
        .page-features li::before {
            content: '•';
            color: #667eea;
            position: absolute;
            left: 0;
        }
        
        .page-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .page-link:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
        }
        
        .info-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 40px;
            color: white;
        }
        
        .info-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .info-content {
            line-height: 1.6;
            opacity: 0.9;
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .tech-tag {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
        }
        
        @media (max-width: 768px) {
            .page-grid {
                grid-template-columns: 1fr;
            }
            
            .title {
                font-size: 28px;
            }
            
            .subtitle {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部信息 -->
        <div class="header">
            <h1 class="title">民航招飞管理系统</h1>
            <p class="subtitle">高保真原型设计 - 页面导航</p>
        </div>
        
        <!-- 项目信息 -->
        <div class="info-section">
            <h2 class="info-title">项目概述</h2>
            <div class="info-content">
                这是一个基于 ElementUI 的民航招飞管理系统高保真原型设计，包含完整的用户界面和交互流程。
                系统采用现代化的 Web 设计规范，注重用户体验和视觉效果，覆盖了招飞全流程的功能模块。
                <br><br>
                <strong>重要提示：</strong>系统已实现登录权限控制，部分功能需要登录后才能访问。
                <a href="login-demo.html" style="color: #409eff; text-decoration: none; margin-left: 10px;">
                    <i class="el-icon-right"></i> 点击这里快速登录体验
                </a>
            </div>
            <div class="tech-stack">
                <span class="tech-tag">Vue.js 2.x</span>
                <span class="tech-tag">ElementUI</span>
                <span class="tech-tag">响应式设计</span>
                <span class="tech-tag">现代化UI</span>
                <span class="tech-tag">高保真原型</span>
                <span class="tech-tag">权限控制</span>
            </div>
        </div>
        
        <!-- 页面导航 -->
        <div class="page-grid">
            <!-- 系统首页 -->
            <div class="page-card">
                <div class="page-icon">
                    <i class="el-icon-s-home"></i>
                </div>
                <h3 class="page-title">系统首页</h3>
                <p class="page-desc">系统入口页面，展示系统特色和功能介绍</p>
                <ul class="page-features">
                    <li>现代化英雄区域设计</li>
                    <li>功能特色卡片展示</li>
                    <li>统计数据可视化</li>
                    <li>登录注册对话框</li>
                </ul>
                <a href="index.html" class="page-link" target="_blank">查看页面</a>
            </div>
            
            <!-- 在线报名 -->
            <div class="page-card">
                <div class="page-icon">
                    <i class="el-icon-edit-outline"></i>
                </div>
                <h3 class="page-title">在线报名</h3>
                <p class="page-desc">多步骤报名流程，包含信息填写和材料上传</p>
                <ul class="page-features">
                    <li>4步骤进度指示器</li>
                    <li>表单验证和数据收集</li>
                    <li>志愿填报系统</li>
                    <li>材料上传功能</li>
                </ul>
                <a href="pages/registration.html" class="page-link" target="_blank">查看页面</a>
            </div>
            
            <!-- 体检管理 -->
            <div class="page-card">
                <div class="page-icon">
                    <i class="el-icon-monitor"></i>
                </div>
                <h3 class="page-title">体检管理</h3>
                <p class="page-desc">体检全流程管理，从预约到结果查询</p>
                <ul class="page-features">
                    <li>体检流程可视化</li>
                    <li>在线预约系统</li>
                    <li>体检记录查询</li>
                    <li>注意事项指导</li>
                </ul>
                <a href="pages/medical.html" class="page-link" target="_blank">查看页面</a>
            </div>
            
            <!-- 面试管理 -->
            <div class="page-card">
                <div class="page-icon">
                    <i class="el-icon-chat-dot-round"></i>
                </div>
                <h3 class="page-title">面试管理</h3>
                <p class="page-desc">面试安排和结果管理，包含准备指导</p>
                <ul class="page-features">
                    <li>面试流程跟踪</li>
                    <li>面试安排确认</li>
                    <li>评分结果展示</li>
                    <li>面试准备指导</li>
                </ul>
                <a href="pages/interview.html" class="page-link" target="_blank">查看页面</a>
            </div>
            
            <!-- 状态查询 -->
            <div class="page-card">
                <div class="page-icon">
                    <i class="el-icon-search"></i>
                </div>
                <h3 class="page-title">状态查询</h3>
                <p class="page-desc">申请状态实时查询和进度跟踪</p>
                <ul class="page-features">
                    <li>状态概览卡片</li>
                    <li>进度时间线</li>
                    <li>通知消息中心</li>
                    <li>快捷操作按钮</li>
                </ul>
                <a href="pages/status.html" class="page-link" target="_blank">查看页面</a>
            </div>
            
            <!-- 学生个人中心 -->
            <div class="page-card">
                <div class="page-icon">
                    <i class="el-icon-user"></i>
                </div>
                <h3 class="page-title">学生个人中心</h3>
                <p class="page-desc">个人信息和申请进度管理中心</p>
                <ul class="page-features">
                    <li>个人信息展示</li>
                    <li>申请进度跟踪</li>
                    <li>快捷操作入口</li>
                    <li>通知消息管理</li>
                </ul>
                <a href="pages/student-dashboard.html" class="page-link" target="_blank">查看页面</a>
            </div>
            
            <!-- 管理员后台 -->
            <div class="page-card">
                <div class="page-icon">
                    <i class="el-icon-s-operation"></i>
                </div>
                <h3 class="page-title">管理员后台</h3>
                <p class="page-desc">系统管理和数据分析后台界面</p>
                <ul class="page-features">
                    <li>数据统计仪表盘</li>
                    <li>申请审核管理</li>
                    <li>图表数据展示</li>
                    <li>系统设置管理</li>
                </ul>
                <a href="pages/admin-dashboard.html" class="page-link" target="_blank">查看页面</a>
            </div>
            
            <!-- 登录演示 -->
            <div class="page-card">
                <div class="page-icon">
                    <i class="el-icon-key"></i>
                </div>
                <h3 class="page-title">登录演示</h3>
                <p class="page-desc">快速登录体验不同用户角色功能</p>
                <ul class="page-features">
                    <li>学生用户快速登录</li>
                    <li>管理员权限体验</li>
                    <li>权限控制演示</li>
                    <li>功能说明指导</li>
                </ul>
                <a href="login-demo.html" class="page-link" target="_blank">快速登录</a>
            </div>

            <!-- 项目文档 -->
            <div class="page-card">
                <div class="page-icon">
                    <i class="el-icon-document"></i>
                </div>
                <h3 class="page-title">项目文档</h3>
                <p class="page-desc">详细的项目说明和技术文档</p>
                <ul class="page-features">
                    <li>系统架构说明</li>
                    <li>功能模块介绍</li>
                    <li>技术实现细节</li>
                    <li>使用说明指南</li>
                </ul>
                <a href="README.md" class="page-link" target="_blank">查看文档</a>
            </div>
        </div>
    </div>
</body>
</html>
