<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录演示 - 民航招飞管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .demo-container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
            max-width: 600px;
            width: 100%;
        }
        
        .demo-title {
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 10px;
        }
        
        .demo-subtitle {
            text-align: center;
            color: #606266;
            margin-bottom: 40px;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 15px;
        }
        
        .demo-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .demo-card {
            border: 1px solid #ebeef5;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s;
        }
        
        .demo-card:hover {
            border-color: #409eff;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }
        
        .demo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 20px;
            color: white;
        }
        
        .demo-role {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 10px;
        }
        
        .demo-desc {
            color: #606266;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .demo-credentials {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 12px;
            color: #606266;
        }
        
        .demo-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .demo-btn:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
        }
        
        .info-section {
            background: #f0f9ff;
            border: 1px solid #b3d8ff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .info-title {
            color: #409eff;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .info-content {
            color: #606266;
            line-height: 1.6;
        }
        
        .back-btn {
            text-align: center;
            margin-top: 30px;
        }
        
        .back-link {
            color: #409eff;
            text-decoration: none;
            font-size: 14px;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="demo-container">
            <h1 class="demo-title">登录演示</h1>
            <p class="demo-subtitle">选择不同的用户角色进行登录体验</p>
            
            <div class="info-section">
                <div class="info-title">
                    <i class="el-icon-info"></i>
                    使用说明
                </div>
                <div class="info-content">
                    点击下方任意角色卡片即可快速登录，体验不同用户角色的功能界面。
                    登录后可以访问相应的功能模块，未登录用户将被重定向到登录页面。
                </div>
            </div>
            
            <div class="demo-section">
                <div class="section-title">选择用户角色</div>
                <div class="demo-cards">
                    <!-- 学生用户 -->
                    <div class="demo-card">
                        <div class="demo-icon">
                            <i class="el-icon-user"></i>
                        </div>
                        <div class="demo-role">学生用户</div>
                        <div class="demo-desc">体验报名、体检、面试等学生功能</div>
                        <div class="demo-credentials">
                            用户名: student<br>
                            密码: 123456<br>
                            类型: 考生
                        </div>
                        <button class="demo-btn" @click="quickLogin('student', 'student')">
                            快速登录
                        </button>
                    </div>
                    
                    <!-- 管理员用户 -->
                    <div class="demo-card">
                        <div class="demo-icon">
                            <i class="el-icon-s-custom"></i>
                        </div>
                        <div class="demo-role">管理员</div>
                        <div class="demo-desc">体验系统管理、数据统计等后台功能</div>
                        <div class="demo-credentials">
                            用户名: admin<br>
                            密码: 123456<br>
                            类型: 管理员
                        </div>
                        <button class="demo-btn" @click="quickLogin('admin', 'admin')">
                            快速登录
                        </button>
                    </div>
                    
                    <!-- 体检医生 -->
                    <div class="demo-card">
                        <div class="demo-icon">
                            <i class="el-icon-monitor"></i>
                        </div>
                        <div class="demo-role">体检医生</div>
                        <div class="demo-desc">体验体检管理和结果录入功能</div>
                        <div class="demo-credentials">
                            用户名: doctor<br>
                            密码: 123456<br>
                            类型: 体检医生
                        </div>
                        <button class="demo-btn" @click="quickLogin('doctor', 'doctor')">
                            快速登录
                        </button>
                    </div>
                    
                    <!-- 面试官 -->
                    <div class="demo-card">
                        <div class="demo-icon">
                            <i class="el-icon-chat-dot-round"></i>
                        </div>
                        <div class="demo-role">面试官</div>
                        <div class="demo-desc">体验面试安排和评分功能</div>
                        <div class="demo-credentials">
                            用户名: interviewer<br>
                            密码: 123456<br>
                            类型: 面试官
                        </div>
                        <button class="demo-btn" @click="quickLogin('interviewer', 'interviewer')">
                            快速登录
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="demo-section">
                <div class="section-title">功能说明</div>
                <el-collapse v-model="activeNames">
                    <el-collapse-item title="学生用户功能" name="student">
                        <div>
                            <ul style="padding-left: 20px; color: #606266;">
                                <li>在线报名：多步骤报名流程，志愿填报</li>
                                <li>体检管理：体检预约、结果查询</li>
                                <li>面试管理：面试安排、结果查看</li>
                                <li>状态查询：申请进度跟踪</li>
                                <li>个人中心：个人信息管理</li>
                            </ul>
                        </div>
                    </el-collapse-item>
                    
                    <el-collapse-item title="管理员功能" name="admin">
                        <div>
                            <ul style="padding-left: 20px; color: #606266;">
                                <li>数据统计：申请数据分析和报表</li>
                                <li>申请管理：审核申请材料</li>
                                <li>用户管理：用户信息管理</li>
                                <li>系统设置：系统配置管理</li>
                                <li>流程管理：招飞流程控制</li>
                            </ul>
                        </div>
                    </el-collapse-item>
                    
                    <el-collapse-item title="权限控制" name="auth">
                        <div>
                            <ul style="padding-left: 20px; color: #606266;">
                                <li>未登录用户只能查看首页和公开信息</li>
                                <li>登录后才能访问相应的功能模块</li>
                                <li>不同角色有不同的功能权限</li>
                                <li>管理员后台需要管理员权限</li>
                                <li>登录状态会保存在本地存储中</li>
                            </ul>
                        </div>
                    </el-collapse-item>
                </el-collapse>
            </div>
            
            <div class="back-btn">
                <a href="index.html" class="back-link">
                    <i class="el-icon-back"></i>
                    返回首页
                </a>
                <span style="margin: 0 15px;">|</span>
                <a href="navigation.html" class="back-link">
                    <i class="el-icon-menu"></i>
                    页面导航
                </a>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    activeNames: ['student']
                }
            },
            methods: {
                quickLogin(username, userType) {
                    // 设置登录状态
                    const user = {
                        username: username,
                        userType: userType
                    };
                    
                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('currentUser', JSON.stringify(user));
                    
                    this.$message.success(`已登录为${this.getUserTypeName(userType)}：${username}`);
                    
                    // 根据用户类型跳转
                    setTimeout(() => {
                        switch(userType) {
                            case 'admin':
                                window.location.href = 'pages/admin-dashboard.html';
                                break;
                            case 'student':
                                window.location.href = 'pages/student-dashboard.html';
                                break;
                            default:
                                window.location.href = 'pages/student-dashboard.html';
                        }
                    }, 1000);
                },
                getUserTypeName(userType) {
                    const typeMap = {
                        'student': '学生',
                        'admin': '管理员',
                        'doctor': '体检医生',
                        'interviewer': '面试官'
                    };
                    return typeMap[userType] || userType;
                }
            }
        });
    </script>
</body>
</html>
