<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员后台 - 民航招飞管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="../css/common.css">
    <script>
        // 管理员页面需要特殊的权限检查
        function checkAdminAuth() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const currentUser = localStorage.getItem('currentUser');

            if (isLoggedIn !== 'true' || !currentUser) {
                alert('请先登录后再访问该功能');
                window.location.href = '../index.html';
                return false;
            }

            const user = JSON.parse(currentUser);
            if (user.userType !== 'admin') {
                alert('您没有权限访问管理员后台');
                window.location.href = '../index.html';
                return false;
            }

            return user;
        }

        // 页面加载时检查权限
        document.addEventListener('DOMContentLoaded', function() {
            checkAdminAuth();
        });
    </script>
    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #304156;
            color: white;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #434a5a;
            text-align: center;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-item {
            display: block;
            padding: 12px 20px;
            color: #bfcbd9;
            text-decoration: none;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover,
        .menu-item.active {
            background-color: #263445;
            color: white;
            border-left-color: #409eff;
        }
        
        .menu-item i {
            margin-right: 10px;
            width: 16px;
        }
        
        .main-panel {
            flex: 1;
            background: #f5f7fa;
            overflow-y: auto;
        }
        
        .admin-header {
            background: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }
        
        .admin-content {
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .stat-title {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 5px;
        }
        
        .stat-desc {
            font-size: 12px;
            color: #909399;
        }
        
        .stat-increase {
            color: #67c23a;
        }
        
        .stat-decrease {
            color: #f56c6c;
        }
        
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 20px;
        }
        
        .chart-placeholder {
            height: 300px;
            background: #f5f7fa;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #909399;
            font-size: 14px;
        }
        
        .recent-activities {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f5f7fa;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            color: white;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-size: 14px;
            color: #303133;
            margin-bottom: 2px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #909399;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .action-btn {
            background: white;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            color: #303133;
        }
        
        .action-btn:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
            color: #409eff;
        }
        
        .action-icon {
            font-size: 24px;
            margin-bottom: 10px;
            display: block;
        }
        
        .action-title {
            font-size: 14px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="admin-layout">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <h3 class="sidebar-title">管理员后台</h3>
                </div>
                
                <nav class="sidebar-menu">
                    <a href="#" class="menu-item active" @click="currentView = 'dashboard'">
                        <i class="el-icon-s-home"></i>
                        仪表盘
                    </a>
                    <a href="#" class="menu-item" @click="currentView = 'applications'">
                        <i class="el-icon-document"></i>
                        申请管理
                    </a>
                    <a href="#" class="menu-item" @click="currentView = 'medical'">
                        <i class="el-icon-monitor"></i>
                        体检管理
                    </a>
                    <a href="#" class="menu-item" @click="currentView = 'interview'">
                        <i class="el-icon-chat-dot-round"></i>
                        面试管理
                    </a>
                    <a href="#" class="menu-item" @click="currentView = 'users'">
                        <i class="el-icon-user"></i>
                        用户管理
                    </a>
                    <a href="#" class="menu-item" @click="currentView = 'reports'">
                        <i class="el-icon-data-analysis"></i>
                        数据报表
                    </a>
                    <a href="#" class="menu-item" @click="currentView = 'settings'">
                        <i class="el-icon-setting"></i>
                        系统设置
                    </a>
                </nav>
            </div>
            
            <!-- 主面板 -->
            <div class="main-panel">
                <!-- 头部 -->
                <div class="admin-header">
                    <h2 style="margin: 0; color: #303133;">{{ getViewTitle() }}</h2>
                    <div>
                        <span style="margin-right: 15px;">欢迎，管理员</span>
                        <el-button type="text" @click="logout">
                            <i class="el-icon-switch-button"></i> 退出
                        </el-button>
                    </div>
                </div>
                
                <!-- 内容区域 -->
                <div class="admin-content">
                    <!-- 仪表盘 -->
                    <div v-show="currentView === 'dashboard'">
                        <!-- 快捷操作 -->
                        <div class="quick-actions">
                            <a href="#" class="action-btn" @click="currentView = 'applications'">
                                <i class="el-icon-document action-icon"></i>
                                <div class="action-title">申请审核</div>
                            </a>
                            <a href="#" class="action-btn" @click="currentView = 'medical'">
                                <i class="el-icon-monitor action-icon"></i>
                                <div class="action-title">体检安排</div>
                            </a>
                            <a href="#" class="action-btn" @click="currentView = 'interview'">
                                <i class="el-icon-chat-dot-round action-icon"></i>
                                <div class="action-title">面试安排</div>
                            </a>
                            <a href="#" class="action-btn" @click="currentView = 'reports'">
                                <i class="el-icon-data-analysis action-icon"></i>
                                <div class="action-title">数据报表</div>
                            </a>
                        </div>
                        
                        <!-- 统计卡片 -->
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-title">总申请数</div>
                                    <div class="stat-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                                        <i class="el-icon-document"></i>
                                    </div>
                                </div>
                                <div class="stat-number">1,234</div>
                                <div class="stat-desc">
                                    <span class="stat-increase">+12%</span> 较上月
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-title">待审核</div>
                                    <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                                        <i class="el-icon-warning"></i>
                                    </div>
                                </div>
                                <div class="stat-number">89</div>
                                <div class="stat-desc">
                                    <span class="stat-decrease">-5%</span> 较昨日
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-title">体检通过</div>
                                    <div class="stat-icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                                        <i class="el-icon-check"></i>
                                    </div>
                                </div>
                                <div class="stat-number">567</div>
                                <div class="stat-desc">
                                    <span class="stat-increase">+8%</span> 较上月
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-title">面试通过</div>
                                    <div class="stat-icon" style="background: linear-gradient(135deg, #43e97b, #38f9d7);">
                                        <i class="el-icon-star-on"></i>
                                    </div>
                                </div>
                                <div class="stat-number">234</div>
                                <div class="stat-desc">
                                    <span class="stat-increase">+15%</span> 较上月
                                </div>
                            </div>
                        </div>
                        
                        <!-- 图表区域 -->
                        <el-row :gutter="20">
                            <el-col :span="16">
                                <div class="chart-container">
                                    <div class="chart-title">申请趋势</div>
                                    <div class="chart-placeholder">
                                        申请数量趋势图 (可集成 ECharts 或其他图表库)
                                    </div>
                                </div>
                            </el-col>
                            
                            <el-col :span="8">
                                <div class="recent-activities">
                                    <div class="chart-title">最近活动</div>
                                    
                                    <div class="activity-item">
                                        <div class="activity-icon" style="background: #409eff;">
                                            <i class="el-icon-user"></i>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">新用户注册</div>
                                            <div class="activity-time">5分钟前</div>
                                        </div>
                                    </div>
                                    
                                    <div class="activity-item">
                                        <div class="activity-icon" style="background: #67c23a;">
                                            <i class="el-icon-check"></i>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">申请审核通过</div>
                                            <div class="activity-time">10分钟前</div>
                                        </div>
                                    </div>
                                    
                                    <div class="activity-item">
                                        <div class="activity-icon" style="background: #e6a23c;">
                                            <i class="el-icon-time"></i>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">体检预约</div>
                                            <div class="activity-time">15分钟前</div>
                                        </div>
                                    </div>
                                    
                                    <div class="activity-item">
                                        <div class="activity-icon" style="background: #f56c6c;">
                                            <i class="el-icon-warning"></i>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">系统异常报警</div>
                                            <div class="activity-time">30分钟前</div>
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                    
                    <!-- 申请管理 -->
                    <div v-show="currentView === 'applications'">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">申请列表</h3>
                                <div>
                                    <el-button type="primary" size="small">
                                        <i class="el-icon-download"></i> 导出数据
                                    </el-button>
                                </div>
                            </div>
                            
                            <!-- 筛选条件 -->
                            <div style="margin-bottom: 20px;">
                                <el-form :inline="true">
                                    <el-form-item label="状态">
                                        <el-select v-model="filters.status" placeholder="选择状态" clearable>
                                            <el-option label="待审核" value="pending"></el-option>
                                            <el-option label="已通过" value="approved"></el-option>
                                            <el-option label="已拒绝" value="rejected"></el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="申请时间">
                                        <el-date-picker
                                            v-model="filters.dateRange"
                                            type="daterange"
                                            placeholder="选择日期范围">
                                        </el-date-picker>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="searchApplications">查询</el-button>
                                        <el-button @click="resetFilters">重置</el-button>
                                    </el-form-item>
                                </el-form>
                            </div>
                            
                            <!-- 申请表格 -->
                            <el-table :data="applications" style="width: 100%">
                                <el-table-column prop="id" label="申请编号" width="120"></el-table-column>
                                <el-table-column prop="name" label="姓名" width="100"></el-table-column>
                                <el-table-column prop="phone" label="手机号" width="120"></el-table-column>
                                <el-table-column prop="company" label="第一志愿" width="150"></el-table-column>
                                <el-table-column prop="submitTime" label="申请时间" width="150"></el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getStatusType(scope.row.status)">
                                            {{ getStatusText(scope.row.status) }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="200">
                                    <template slot-scope="scope">
                                        <el-button type="text" size="small" @click="viewApplication(scope.row)">
                                            查看
                                        </el-button>
                                        <el-button v-if="scope.row.status === 'pending'" type="text" size="small" @click="approveApplication(scope.row)">
                                            通过
                                        </el-button>
                                        <el-button v-if="scope.row.status === 'pending'" type="text" size="small" @click="rejectApplication(scope.row)">
                                            拒绝
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            
                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handlePageChange"
                                    :current-page="currentPage"
                                    :page-size="pageSize"
                                    layout="total, prev, pager, next"
                                    :total="totalApplications">
                                </el-pagination>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 其他视图占位 -->
                    <div v-show="currentView !== 'dashboard' && currentView !== 'applications'">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">{{ getViewTitle() }}</h3>
                            </div>
                            <div style="padding: 40px; text-align: center; color: #909399;">
                                {{ getViewTitle() }}功能开发中...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    currentView: 'dashboard',
                    filters: {
                        status: '',
                        dateRange: []
                    },
                    currentPage: 1,
                    pageSize: 10,
                    totalApplications: 100,
                    applications: [
                        {
                            id: 'ZF2024001234',
                            name: '张三',
                            phone: '13800138000',
                            company: '中国国际航空',
                            submitTime: '2024-03-01',
                            status: 'pending'
                        },
                        {
                            id: 'ZF2024001235',
                            name: '李四',
                            phone: '13800138001',
                            company: '中国东方航空',
                            submitTime: '2024-03-02',
                            status: 'approved'
                        },
                        {
                            id: 'ZF2024001236',
                            name: '王五',
                            phone: '13800138002',
                            company: '中国南方航空',
                            submitTime: '2024-03-03',
                            status: 'rejected'
                        }
                    ]
                }
            },
            methods: {
                getViewTitle() {
                    const titles = {
                        'dashboard': '仪表盘',
                        'applications': '申请管理',
                        'medical': '体检管理',
                        'interview': '面试管理',
                        'users': '用户管理',
                        'reports': '数据报表',
                        'settings': '系统设置'
                    };
                    return titles[this.currentView] || '未知页面';
                },
                getStatusType(status) {
                    const types = {
                        'pending': 'warning',
                        'approved': 'success',
                        'rejected': 'danger'
                    };
                    return types[status] || 'info';
                },
                getStatusText(status) {
                    const texts = {
                        'pending': '待审核',
                        'approved': '已通过',
                        'rejected': '已拒绝'
                    };
                    return texts[status] || status;
                },
                searchApplications() {
                    this.$message.info('查询功能开发中...');
                },
                resetFilters() {
                    this.filters = {
                        status: '',
                        dateRange: []
                    };
                },
                viewApplication(row) {
                    this.$message.info('查看申请：' + row.name);
                },
                approveApplication(row) {
                    this.$confirm('确认通过此申请？', '确认操作', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'info'
                    }).then(() => {
                        row.status = 'approved';
                        this.$message.success('申请已通过');
                    });
                },
                rejectApplication(row) {
                    this.$prompt('请输入拒绝原因', '拒绝申请', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        inputType: 'textarea'
                    }).then(({ value }) => {
                        row.status = 'rejected';
                        this.$message.success('申请已拒绝');
                    });
                },
                handlePageChange(page) {
                    this.currentPage = page;
                },
                logout() {
                    this.$confirm('确认退出登录？', '确认退出', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        localStorage.removeItem('isLoggedIn');
                        localStorage.removeItem('currentUser');
                        window.location.href = '../index.html';
                    });
                }
            }
        });
    </script>
</body>
</html>
