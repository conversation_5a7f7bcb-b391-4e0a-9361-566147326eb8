<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 民航招飞管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="../css/common.css">
    <script src="../js/auth.js"></script>
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            margin-bottom: 30px;
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
        }
        
        .user-info h2 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        
        .user-info p {
            margin: 0;
            opacity: 0.9;
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        
        .stat-item:hover {
            transform: translateY(-2px);
        }
        
        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 20px;
            color: white;
        }
        
        .stat-title {
            font-size: 14px;
            color: #606266;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #303133;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }
        
        .main-content-area {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .sidebar-area {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .progress-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .progress-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 20px;
        }
        
        .progress-step {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f5f7fa;
        }
        
        .progress-step:last-child {
            border-bottom: none;
        }
        
        .step-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 14px;
            color: white;
        }
        
        .step-completed {
            background-color: #67c23a;
        }
        
        .step-current {
            background-color: #409eff;
        }
        
        .step-pending {
            background-color: #e4e7ed;
            color: #909399;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 2px;
        }
        
        .step-desc {
            font-size: 12px;
            color: #909399;
        }
        
        .notification-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .notification-item {
            display: flex;
            align-items: flex-start;
            padding: 12px 0;
            border-bottom: 1px solid #f5f7fa;
        }
        
        .notification-item:last-child {
            border-bottom: none;
        }
        
        .notification-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-top: 6px;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .notification-new {
            background-color: #f56c6c;
        }
        
        .notification-read {
            background-color: #e4e7ed;
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-title {
            font-size: 14px;
            color: #303133;
            margin-bottom: 4px;
        }
        
        .notification-time {
            font-size: 12px;
            color: #909399;
        }
        
        .quick-actions {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            border: 1px solid #ebeef5;
            border-radius: 8px;
            text-decoration: none;
            color: #303133;
            transition: all 0.3s;
        }
        
        .action-btn:hover {
            border-color: #409eff;
            color: #409eff;
            transform: translateY(-2px);
        }
        
        .action-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .action-text {
            font-size: 12px;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .user-profile {
                flex-direction: column;
                text-align: center;
            }
            
            .action-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    <i class="el-icon-s-promotion"></i>
                    民航招飞系统
                </a>
                
                <nav>
                    <ul class="nav-menu">
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="registration.html">在线报名</a></li>
                        <li><a href="medical.html">体检管理</a></li>
                        <li><a href="interview.html">面试管理</a></li>
                        <li><a href="status.html">状态查询</a></li>
                    </ul>
                </nav>
                
                <div class="user-info">
                    <span style="color: white;">欢迎，<span class="user-name">用户</span></span>
                    <el-button type="text" style="color: white; margin-left: 15px;" @click="handleLogout">
                        <i class="el-icon-switch-button"></i> 退出
                    </el-button>
                </div>
            </div>
        </header>

        <!-- 用户信息头部 -->
        <section class="dashboard-header">
            <div class="main-content">
                <div class="user-profile">
                    <div class="user-avatar">
                        <i class="el-icon-user"></i>
                    </div>
                    <div class="user-info">
                        <h2 class="user-name">用户</h2>
                        <p>申请编号：ZF2024001234</p>
                        <p>第一志愿：中国国际航空 - 中国民航大学</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 主要内容 -->
        <main class="main-content">
            <!-- 快速统计 -->
            <div class="quick-stats">
                <div class="stat-item">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                        <i class="el-icon-document"></i>
                    </div>
                    <div class="stat-title">申请状态</div>
                    <div class="stat-value">面试阶段</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                        <i class="el-icon-time"></i>
                    </div>
                    <div class="stat-title">申请天数</div>
                    <div class="stat-value">45天</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                        <i class="el-icon-check"></i>
                    </div>
                    <div class="stat-title">完成进度</div>
                    <div class="stat-value">75%</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #43e97b, #38f9d7);">
                        <i class="el-icon-message"></i>
                    </div>
                    <div class="stat-title">未读消息</div>
                    <div class="stat-value">3条</div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="dashboard-grid">
                <!-- 左侧主要内容 -->
                <div class="main-content-area">
                    <!-- 申请进度 -->
                    <div class="progress-card">
                        <div class="progress-title">
                            <i class="el-icon-s-operation"></i>
                            申请进度
                        </div>
                        
                        <div class="progress-step">
                            <div class="step-icon step-completed">
                                <i class="el-icon-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-title">在线报名</div>
                                <div class="step-desc">已完成 - 2024-03-01</div>
                            </div>
                        </div>
                        
                        <div class="progress-step">
                            <div class="step-icon step-completed">
                                <i class="el-icon-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-title">初检体检</div>
                                <div class="step-desc">已完成 - 2024-03-15</div>
                            </div>
                        </div>
                        
                        <div class="progress-step">
                            <div class="step-icon step-completed">
                                <i class="el-icon-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-title">复检体检</div>
                                <div class="step-desc">已完成 - 2024-03-25</div>
                            </div>
                        </div>
                        
                        <div class="progress-step">
                            <div class="step-icon step-completed">
                                <i class="el-icon-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-title">初试面试</div>
                                <div class="step-desc">已完成 - 2024-04-15</div>
                            </div>
                        </div>
                        
                        <div class="progress-step">
                            <div class="step-icon step-current">
                                <i class="el-icon-loading"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-title">复试面试</div>
                                <div class="step-desc">进行中 - 2024-04-20</div>
                            </div>
                        </div>
                        
                        <div class="progress-step">
                            <div class="step-icon step-pending">
                                <i class="el-icon-time"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-title">终检体检</div>
                                <div class="step-desc">等待安排</div>
                            </div>
                        </div>
                        
                        <div class="progress-step">
                            <div class="step-icon step-pending">
                                <i class="el-icon-document"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-title">录取确认</div>
                                <div class="step-desc">等待结果</div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近活动 -->
                    <div class="progress-card">
                        <div class="progress-title">
                            <i class="el-icon-time"></i>
                            最近活动
                        </div>
                        
                        <el-timeline>
                            <el-timeline-item timestamp="2024-04-18 10:00" color="#409eff">
                                收到复试面试通知
                            </el-timeline-item>
                            <el-timeline-item timestamp="2024-04-16 16:30" color="#67c23a">
                                初试面试结果公布：通过
                            </el-timeline-item>
                            <el-timeline-item timestamp="2024-04-15 09:00" color="#67c23a">
                                参加初试面试
                            </el-timeline-item>
                            <el-timeline-item timestamp="2024-03-26 09:15" color="#67c23a">
                                复检体检结果：合格
                            </el-timeline-item>
                            <el-timeline-item timestamp="2024-03-25 14:00" color="#67c23a">
                                参加复检体检
                            </el-timeline-item>
                        </el-timeline>
                    </div>
                </div>

                <!-- 右侧边栏 -->
                <div class="sidebar-area">
                    <!-- 快捷操作 -->
                    <div class="quick-actions">
                        <div class="progress-title">
                            <i class="el-icon-s-operation"></i>
                            快捷操作
                        </div>
                        
                        <div class="action-grid">
                            <a href="status.html" class="action-btn">
                                <i class="el-icon-search action-icon"></i>
                                <span class="action-text">状态查询</span>
                            </a>
                            
                            <a href="medical.html" class="action-btn">
                                <i class="el-icon-monitor action-icon"></i>
                                <span class="action-text">体检预约</span>
                            </a>
                            
                            <a href="interview.html" class="action-btn">
                                <i class="el-icon-chat-dot-round action-icon"></i>
                                <span class="action-text">面试安排</span>
                            </a>
                            
                            <a href="#" class="action-btn" @click="downloadMaterials">
                                <i class="el-icon-download action-icon"></i>
                                <span class="action-text">下载材料</span>
                            </a>
                        </div>
                    </div>

                    <!-- 通知消息 -->
                    <div class="notification-card">
                        <div class="progress-title">
                            <i class="el-icon-bell"></i>
                            通知消息
                            <el-badge :value="3" class="item" style="margin-left: 10px;"></el-badge>
                        </div>
                        
                        <div class="notification-item">
                            <div class="notification-dot notification-new"></div>
                            <div class="notification-content">
                                <div class="notification-title">复试面试通知</div>
                                <div class="notification-time">2小时前</div>
                            </div>
                        </div>
                        
                        <div class="notification-item">
                            <div class="notification-dot notification-new"></div>
                            <div class="notification-content">
                                <div class="notification-title">初试成绩公布</div>
                                <div class="notification-time">2天前</div>
                            </div>
                        </div>
                        
                        <div class="notification-item">
                            <div class="notification-dot notification-new"></div>
                            <div class="notification-content">
                                <div class="notification-title">体检结果通知</div>
                                <div class="notification-time">1周前</div>
                            </div>
                        </div>
                        
                        <div class="notification-item">
                            <div class="notification-dot notification-read"></div>
                            <div class="notification-content">
                                <div class="notification-title">申请审核通过</div>
                                <div class="notification-time">3周前</div>
                            </div>
                        </div>
                        
                        <div style="text-align: center; margin-top: 15px;">
                            <el-button type="text" size="small">查看全部</el-button>
                        </div>
                    </div>

                    <!-- 联系信息 -->
                    <div class="notification-card">
                        <div class="progress-title">
                            <i class="el-icon-phone"></i>
                            联系我们
                        </div>
                        
                        <div style="padding: 10px 0;">
                            <p style="margin: 8px 0; color: #606266;">
                                <i class="el-icon-phone" style="margin-right: 8px;"></i>
                                客服热线：************
                            </p>
                            <p style="margin: 8px 0; color: #606266;">
                                <i class="el-icon-message" style="margin-right: 8px;"></i>
                                在线客服：9:00-18:00
                            </p>
                            <p style="margin: 8px 0; color: #606266;">
                                <i class="el-icon-location" style="margin-right: 8px;"></i>
                                地址：北京市朝阳区
                            </p>
                        </div>
                        
                        <el-button type="primary" size="small" style="width: 100%; margin-top: 10px;" @click="contactSupport">
                            联系客服
                        </el-button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    // 数据可以在这里定义
                }
            },
            methods: {
                handleLogout() {
                    this.$confirm('确认退出登录？', '确认退出', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        logout();
                    });
                },
                downloadMaterials() {
                    this.$message.success('材料下载中...');
                },
                contactSupport() {
                    this.$message.info('正在连接客服...');
                }
            }
        });
    </script>
</body>
</html>
