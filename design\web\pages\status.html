<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态查询 - 民航招飞管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="../css/common.css">
    <style>
        .status-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        
        .status-card:hover {
            transform: translateY(-2px);
        }
        
        .status-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            color: white;
        }
        
        .status-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 10px;
        }
        
        .status-desc {
            font-size: 14px;
            color: #606266;
        }
        
        .status-pending .status-icon {
            background: linear-gradient(135deg, #e6a23c, #f7ba2a);
        }
        
        .status-approved .status-icon {
            background: linear-gradient(135deg, #67c23a, #85ce61);
        }
        
        .status-rejected .status-icon {
            background: linear-gradient(135deg, #f56c6c, #f78989);
        }
        
        .status-processing .status-icon {
            background: linear-gradient(135deg, #409eff, #66b1ff);
        }
        
        .timeline-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .progress-timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline-item {
            position: relative;
            padding-bottom: 30px;
        }
        
        .timeline-item:last-child {
            padding-bottom: 0;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 8px;
            width: 2px;
            height: calc(100% - 8px);
            background-color: #e4e7ed;
        }
        
        .timeline-item:last-child::before {
            display: none;
        }
        
        .timeline-dot {
            position: absolute;
            left: -30px;
            top: 4px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #e4e7ed;
            border: 3px solid white;
            box-shadow: 0 0 0 1px #e4e7ed;
        }
        
        .timeline-item.completed .timeline-dot {
            background-color: #67c23a;
            box-shadow: 0 0 0 1px #67c23a;
        }
        
        .timeline-item.current .timeline-dot {
            background-color: #409eff;
            box-shadow: 0 0 0 1px #409eff;
        }
        
        .timeline-content {
            padding-left: 10px;
        }
        
        .timeline-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 5px;
        }
        
        .timeline-time {
            font-size: 12px;
            color: #909399;
            margin-bottom: 8px;
        }
        
        .timeline-desc {
            font-size: 14px;
            color: #606266;
            line-height: 1.5;
        }
        
        .application-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
        }
        
        .info-label {
            font-weight: 500;
            color: #606266;
        }
        
        .info-value {
            color: #303133;
        }
        
        .notification-item {
            border: 1px solid #ebeef5;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            transition: box-shadow 0.3s;
        }
        
        .notification-item:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .notification-title {
            font-weight: 600;
            color: #303133;
        }
        
        .notification-time {
            font-size: 12px;
            color: #909399;
        }
        
        .notification-content {
            color: #606266;
            line-height: 1.5;
        }
        
        .notification-type {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .type-info {
            background-color: #e1f3d8;
            color: #67c23a;
        }
        
        .type-warning {
            background-color: #fdf6ec;
            color: #e6a23c;
        }
        
        .type-success {
            background-color: #f0f9ff;
            color: #409eff;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    <i class="el-icon-s-promotion"></i>
                    民航招飞系统
                </a>
                
                <nav>
                    <ul class="nav-menu">
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="registration.html">在线报名</a></li>
                        <li><a href="medical.html">体检管理</a></li>
                        <li><a href="interview.html">面试管理</a></li>
                        <li><a href="status.html" class="active">状态查询</a></li>
                    </ul>
                </nav>
                
                <div class="user-info">
                    <span style="color: white;">欢迎，张三</span>
                    <el-button type="text" style="color: white; margin-left: 15px;">
                        <i class="el-icon-switch-button"></i> 退出
                    </el-button>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="main-content">
            <h1 class="page-title">
                <i class="el-icon-search"></i>
                状态查询
            </h1>

            <!-- 状态概览 -->
            <div class="status-overview">
                <div class="status-card status-approved">
                    <div class="status-icon">
                        <i class="el-icon-check"></i>
                    </div>
                    <div class="status-title">报名状态</div>
                    <div class="status-desc">已提交，审核通过</div>
                </div>
                
                <div class="status-card status-approved">
                    <div class="status-icon">
                        <i class="el-icon-check"></i>
                    </div>
                    <div class="status-title">体检状态</div>
                    <div class="status-desc">初检合格，复检进行中</div>
                </div>
                
                <div class="status-card status-processing">
                    <div class="status-icon">
                        <i class="el-icon-loading"></i>
                    </div>
                    <div class="status-title">面试状态</div>
                    <div class="status-desc">初试通过，等待复试</div>
                </div>
                
                <div class="status-card status-pending">
                    <div class="status-icon">
                        <i class="el-icon-time"></i>
                    </div>
                    <div class="status-title">录取状态</div>
                    <div class="status-desc">等待最终结果</div>
                </div>
            </div>

            <!-- 申请信息 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">申请信息</h3>
                </div>
                
                <div class="application-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">申请编号：</span>
                            <span class="info-value">ZF2024001234</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">申请时间：</span>
                            <span class="info-value">2024-03-01</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">第一志愿：</span>
                            <span class="info-value">中国国际航空</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">培养院校：</span>
                            <span class="info-value">中国民航大学</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">专业方向：</span>
                            <span class="info-value">飞行技术</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">当前状态：</span>
                            <span class="info-value" style="color: #409eff;">面试阶段</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 进度时间线 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">进度跟踪</h3>
                </div>
                
                <div class="timeline-container">
                    <div class="progress-timeline">
                        <div class="timeline-item completed">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">在线报名</div>
                                <div class="timeline-time">2024-03-01 10:30</div>
                                <div class="timeline-desc">已成功提交报名申请，材料审核通过</div>
                            </div>
                        </div>
                        
                        <div class="timeline-item completed">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">初检体检</div>
                                <div class="timeline-time">2024-03-15 09:00</div>
                                <div class="timeline-desc">初检体检合格，各项指标正常</div>
                            </div>
                        </div>
                        
                        <div class="timeline-item completed">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">复检体检</div>
                                <div class="timeline-time">2024-03-25 14:00</div>
                                <div class="timeline-desc">复检体检合格，符合飞行员体检标准</div>
                            </div>
                        </div>
                        
                        <div class="timeline-item completed">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">初试面试</div>
                                <div class="timeline-time">2024-04-15 09:00</div>
                                <div class="timeline-desc">初试面试通过，综合评分85分</div>
                            </div>
                        </div>
                        
                        <div class="timeline-item current">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">复试面试</div>
                                <div class="timeline-time">2024-04-20 14:00</div>
                                <div class="timeline-desc">复试面试安排已确认，请按时参加</div>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">终检体检</div>
                                <div class="timeline-time">待安排</div>
                                <div class="timeline-desc">等待面试结果，通过后安排终检</div>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">录取确认</div>
                                <div class="timeline-time">待确认</div>
                                <div class="timeline-desc">等待最终录取结果公布</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 通知消息 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">通知消息</h3>
                </div>
                
                <div v-for="notification in notifications" :key="notification.id" class="notification-item">
                    <div class="notification-header">
                        <div>
                            <span class="notification-title">{{ notification.title }}</span>
                            <span :class="['notification-type', `type-${notification.type}`]">
                                {{ getTypeText(notification.type) }}
                            </span>
                        </div>
                        <span class="notification-time">{{ notification.time }}</span>
                    </div>
                    <div class="notification-content">{{ notification.content }}</div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">快捷操作</h3>
                </div>
                
                <div style="text-align: center; padding: 20px;">
                    <el-button type="primary" @click="downloadApplication">
                        <i class="el-icon-download"></i> 下载申请表
                    </el-button>
                    <el-button type="success" @click="printStatus">
                        <i class="el-icon-printer"></i> 打印状态单
                    </el-button>
                    <el-button type="info" @click="contactSupport">
                        <i class="el-icon-phone"></i> 联系客服
                    </el-button>
                    <el-button type="warning" @click="updateInfo">
                        <i class="el-icon-edit"></i> 更新信息
                    </el-button>
                </div>
            </div>
        </main>

        <!-- 联系客服对话框 -->
        <el-dialog title="联系客服" :visible.sync="showContactDialog" width="500px">
            <div>
                <h4>客服热线</h4>
                <p style="margin: 10px 0;">
                    <i class="el-icon-phone"></i> ************
                </p>
                <p style="margin: 10px 0; color: #909399;">
                    服务时间：周一至周五 9:00-18:00
                </p>
                
                <h4 style="margin-top: 20px;">在线咨询</h4>
                <el-form label-width="80px">
                    <el-form-item label="问题类型">
                        <el-select v-model="contactForm.type" placeholder="请选择问题类型">
                            <el-option label="报名问题" value="registration"></el-option>
                            <el-option label="体检问题" value="medical"></el-option>
                            <el-option label="面试问题" value="interview"></el-option>
                            <el-option label="其他问题" value="other"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="问题描述">
                        <el-input v-model="contactForm.message" type="textarea" rows="4" placeholder="请详细描述您的问题"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <div slot="footer">
                <el-button @click="showContactDialog = false">取消</el-button>
                <el-button type="primary" @click="submitContact">提交咨询</el-button>
            </div>
        </el-dialog>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    showContactDialog: false,
                    contactForm: {
                        type: '',
                        message: ''
                    },
                    notifications: [
                        {
                            id: 1,
                            title: '复试面试通知',
                            type: 'info',
                            time: '2024-04-18 10:00',
                            content: '您的复试面试已安排在2024年4月20日14:00，地点：北京市朝阳区国航大厦，请提前30分钟到达。'
                        },
                        {
                            id: 2,
                            title: '初试面试结果',
                            type: 'success',
                            time: '2024-04-16 16:30',
                            content: '恭喜您！初试面试成绩为85分，已通过初试，请关注复试通知。'
                        },
                        {
                            id: 3,
                            title: '体检结果通知',
                            type: 'success',
                            time: '2024-03-26 09:15',
                            content: '您的复检体检结果已出，各项指标均合格，符合飞行员体检标准。'
                        },
                        {
                            id: 4,
                            title: '材料补充提醒',
                            type: 'warning',
                            time: '2024-03-10 14:20',
                            content: '请在3月15日前补充提交学历证明复印件，逾期可能影响后续流程。'
                        }
                    ]
                }
            },
            methods: {
                getTypeText(type) {
                    const typeMap = {
                        'info': '通知',
                        'success': '成功',
                        'warning': '提醒'
                    };
                    return typeMap[type] || type;
                },
                downloadApplication() {
                    this.$message.success('申请表下载中...');
                },
                printStatus() {
                    this.$message.success('状态单打印中...');
                },
                contactSupport() {
                    this.showContactDialog = true;
                },
                updateInfo() {
                    this.$message.info('信息更新功能开发中...');
                },
                submitContact() {
                    if (!this.contactForm.type || !this.contactForm.message) {
                        this.$message.error('请填写完整信息');
                        return;
                    }
                    this.$message.success('咨询已提交，客服将在24小时内回复');
                    this.showContactDialog = false;
                    this.contactForm = { type: '', message: '' };
                }
            }
        });
    </script>
</body>
</html>
