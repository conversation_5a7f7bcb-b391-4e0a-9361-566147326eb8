<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面试管理 - 民航招飞管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="../css/common.css">
    <script src="../js/auth.js"></script>
    <style>
        .interview-card {
            border: 1px solid #ebeef5;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            transition: box-shadow 0.3s;
        }
        
        .interview-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .interview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .interview-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }
        
        .interview-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-upcoming {
            background-color: #e1f3d8;
            color: #67c23a;
        }
        
        .status-completed {
            background-color: #f0f9ff;
            color: #409eff;
        }
        
        .status-cancelled {
            background-color: #fef0f0;
            color: #f56c6c;
        }
        
        .interview-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-label {
            font-weight: 500;
            color: #606266;
        }
        
        .info-value {
            color: #303133;
        }
        
        .score-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f5f7fa;
        }
        
        .score-item:last-child {
            border-bottom: none;
        }
        
        .score-name {
            font-weight: 500;
            color: #303133;
        }
        
        .score-value {
            font-weight: 600;
            color: #409eff;
        }
        
        .preparation-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .preparation-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 15px;
        }
        
        .preparation-list {
            list-style: none;
            padding: 0;
        }
        
        .preparation-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .preparation-list li:last-child {
            border-bottom: none;
        }
        
        .check-icon {
            color: #67c23a;
        }
        
        .interview-flow {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .flow-step {
            flex: 1;
            text-align: center;
            position: relative;
        }
        
        .flow-step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 20px;
            right: -50%;
            width: 100%;
            height: 2px;
            background-color: #e4e7ed;
            z-index: 1;
        }
        
        .flow-step.completed::after {
            background-color: #67c23a;
        }
        
        .flow-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e4e7ed;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            position: relative;
            z-index: 2;
        }
        
        .flow-step.completed .flow-icon {
            background-color: #67c23a;
        }
        
        .flow-step.current .flow-icon {
            background-color: #409eff;
        }
        
        .flow-label {
            font-size: 14px;
            color: #606266;
        }
        
        .flow-step.completed .flow-label,
        .flow-step.current .flow-label {
            color: #303133;
            font-weight: 500;
        }
        
        .feedback-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .feedback-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 15px;
        }
        
        .feedback-content {
            color: #606266;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    <i class="el-icon-s-promotion"></i>
                    民航招飞系统
                </a>
                
                <nav>
                    <ul class="nav-menu">
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="registration.html">在线报名</a></li>
                        <li><a href="medical.html">体检管理</a></li>
                        <li><a href="interview.html" class="active">面试管理</a></li>
                        <li><a href="status.html">状态查询</a></li>
                    </ul>
                </nav>
                
                <div class="user-info">
                    <span style="color: white;">欢迎，<span class="user-name">用户</span></span>
                    <el-button type="text" style="color: white; margin-left: 15px;" @click="handleLogout">
                        <i class="el-icon-switch-button"></i> 退出
                    </el-button>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="main-content">
            <h1 class="page-title">
                <i class="el-icon-chat-dot-round"></i>
                面试管理
            </h1>

            <!-- 面试流程 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">面试流程</h3>
                </div>
                
                <div class="interview-flow">
                    <div class="flow-step completed">
                        <div class="flow-icon">
                            <i class="el-icon-check"></i>
                        </div>
                        <div class="flow-label">面试通知</div>
                    </div>
                    <div class="flow-step completed">
                        <div class="flow-icon">
                            <i class="el-icon-check"></i>
                        </div>
                        <div class="flow-label">确认参加</div>
                    </div>
                    <div class="flow-step current">
                        <div class="flow-icon">
                            <i class="el-icon-loading"></i>
                        </div>
                        <div class="flow-label">面试进行</div>
                    </div>
                    <div class="flow-step">
                        <div class="flow-icon">
                            <i class="el-icon-document"></i>
                        </div>
                        <div class="flow-label">结果公布</div>
                    </div>
                </div>
            </div>

            <!-- 标签页 -->
            <div class="card">
                <el-tabs v-model="activeTab">
                    <el-tab-pane label="面试安排" name="schedule">
                        <div v-for="interview in interviews" :key="interview.id" class="interview-card">
                            <div class="interview-header">
                                <div class="interview-title">{{ interview.type }} - {{ interview.company }}</div>
                                <div :class="['interview-status', `status-${interview.status}`]">
                                    {{ getStatusText(interview.status) }}
                                </div>
                            </div>
                            
                            <div class="interview-info">
                                <div class="info-item">
                                    <i class="el-icon-time"></i>
                                    <span class="info-label">面试时间：</span>
                                    <span class="info-value">{{ interview.datetime }}</span>
                                </div>
                                <div class="info-item">
                                    <i class="el-icon-location"></i>
                                    <span class="info-label">面试地点：</span>
                                    <span class="info-value">{{ interview.location }}</span>
                                </div>
                                <div class="info-item">
                                    <i class="el-icon-user"></i>
                                    <span class="info-label">面试官：</span>
                                    <span class="info-value">{{ interview.interviewer }}</span>
                                </div>
                                <div class="info-item">
                                    <i class="el-icon-phone"></i>
                                    <span class="info-label">联系电话：</span>
                                    <span class="info-value">{{ interview.phone }}</span>
                                </div>
                            </div>
                            
                            <div style="margin-top: 15px;">
                                <el-button type="primary" size="small" @click="viewDetails(interview)">
                                    查看详情
                                </el-button>
                                <el-button v-if="interview.status === 'upcoming'" type="success" size="small" @click="confirmAttendance(interview)">
                                    确认参加
                                </el-button>
                                <el-button v-if="interview.status === 'upcoming'" type="warning" size="small" @click="requestReschedule(interview)">
                                    申请改期
                                </el-button>
                            </div>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="面试结果" name="results">
                        <div v-for="result in interviewResults" :key="result.id" class="interview-card">
                            <div class="interview-header">
                                <div class="interview-title">{{ result.type }} 面试结果</div>
                                <div :class="['interview-status', result.overall === 'pass' ? 'status-completed' : 'status-cancelled']">
                                    {{ result.overall === 'pass' ? '通过' : '未通过' }}
                                </div>
                            </div>
                            
                            <div class="interview-info">
                                <div class="info-item">
                                    <i class="el-icon-time"></i>
                                    <span class="info-label">面试时间：</span>
                                    <span class="info-value">{{ result.datetime }}</span>
                                </div>
                                <div class="info-item">
                                    <i class="el-icon-user"></i>
                                    <span class="info-label">面试官：</span>
                                    <span class="info-value">{{ result.interviewer }}</span>
                                </div>
                                <div class="info-item">
                                    <i class="el-icon-star-on"></i>
                                    <span class="info-label">总分：</span>
                                    <span class="info-value">{{ result.totalScore }}/100</span>
                                </div>
                            </div>
                            
                            <h4 style="margin: 15px 0;">评分详情</h4>
                            <div v-for="score in result.scores" :key="score.name" class="score-item">
                                <span class="score-name">{{ score.name }}</span>
                                <span class="score-value">{{ score.score }}/{{ score.maxScore }}</span>
                            </div>
                            
                            <div v-if="result.feedback" class="feedback-card">
                                <div class="feedback-title">面试官评价</div>
                                <div class="feedback-content">{{ result.feedback }}</div>
                            </div>
                            
                            <div style="margin-top: 15px;">
                                <el-button type="primary" size="small" @click="downloadCertificate(result)">
                                    <i class="el-icon-download"></i> 下载成绩单
                                </el-button>
                                <el-button v-if="result.overall !== 'pass'" type="info" size="small" @click="viewImprovement(result)">
                                    <i class="el-icon-view"></i> 改进建议
                                </el-button>
                            </div>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="面试准备" name="preparation">
                        <div class="preparation-section">
                            <div class="preparation-title">
                                <i class="el-icon-document"></i>
                                面试材料准备
                            </div>
                            <ul class="preparation-list">
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    身份证原件及复印件
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    学历证明原件及复印件
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    体检合格证明
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    近期免冠照片2张
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    面试通知书
                                </li>
                            </ul>
                        </div>
                        
                        <div class="preparation-section">
                            <div class="preparation-title">
                                <i class="el-icon-user"></i>
                                仪表仪容要求
                            </div>
                            <ul class="preparation-list">
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    着装整洁，建议穿正装或商务休闲装
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    发型整齐，男生不留长发，女生可扎马尾
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    保持良好的精神状态
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    注意个人卫生，避免浓重香水
                                </li>
                            </ul>
                        </div>
                        
                        <div class="preparation-section">
                            <div class="preparation-title">
                                <i class="el-icon-chat-dot-round"></i>
                                面试内容准备
                            </div>
                            <ul class="preparation-list">
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    自我介绍（1-2分钟）
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    为什么选择飞行员职业
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    对民航行业的了解
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    英语口语表达能力
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    应急处理能力测试
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    团队协作能力展示
                                </li>
                            </ul>
                        </div>
                        
                        <div class="preparation-section">
                            <div class="preparation-title">
                                <i class="el-icon-warning"></i>
                                注意事项
                            </div>
                            <ul class="preparation-list">
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    提前30分钟到达面试地点
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    保持手机畅通，关注面试通知
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    诚实回答问题，不要夸大或隐瞒
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    保持自信和礼貌的态度
                                </li>
                                <li>
                                    <i class="el-icon-check check-icon"></i>
                                    如有特殊情况及时联系招飞办
                                </li>
                            </ul>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="常见问题" name="faq">
                        <el-collapse v-model="activeFaq">
                            <el-collapse-item title="面试一般包含哪些环节？" name="process">
                                <div>
                                    <p>面试通常包括以下环节：</p>
                                    <ul>
                                        <li><strong>个人面试：</strong>自我介绍、职业规划、基本素质考察</li>
                                        <li><strong>英语面试：</strong>英语口语表达能力测试</li>
                                        <li><strong>心理测试：</strong>心理素质和抗压能力评估</li>
                                        <li><strong>团队协作：</strong>小组讨论或团队任务</li>
                                        <li><strong>专业知识：</strong>航空相关基础知识问答</li>
                                    </ul>
                                </div>
                            </el-collapse-item>
                            
                            <el-collapse-item title="面试评分标准是什么？" name="scoring">
                                <div>
                                    <p>面试评分主要考察以下几个方面：</p>
                                    <ul>
                                        <li><strong>综合素质（30分）：</strong>仪表仪容、语言表达、逻辑思维</li>
                                        <li><strong>专业素养（25分）：</strong>对飞行员职业的理解和认知</li>
                                        <li><strong>英语能力（20分）：</strong>英语口语表达和理解能力</li>
                                        <li><strong>心理素质（15分）：</strong>抗压能力、应变能力</li>
                                        <li><strong>团队协作（10分）：</strong>沟通协调、团队合作能力</li>
                                    </ul>
                                    <p>总分100分，一般要求达到70分以上为合格。</p>
                                </div>
                            </el-collapse-item>
                            
                            <el-collapse-item title="面试结果什么时候公布？" name="results">
                                <div>
                                    <p>面试结果公布时间：</p>
                                    <ul>
                                        <li>面试结束后3-5个工作日内公布初步结果</li>
                                        <li>可通过系统查询或电话咨询</li>
                                        <li>最终录取结果将在所有环节完成后统一公布</li>
                                        <li>如有疑问可联系招飞办进行咨询</li>
                                    </ul>
                                </div>
                            </el-collapse-item>
                            
                            <el-collapse-item title="面试不通过可以申请复试吗？" name="retake">
                                <div>
                                    <p>关于复试的规定：</p>
                                    <ul>
                                        <li>一般情况下面试只有一次机会</li>
                                        <li>如因特殊原因（如生病、突发事件）影响发挥，可申请复试</li>
                                        <li>复试申请需在结果公布后3个工作日内提出</li>
                                        <li>复试机会有限，需要充分的理由和证明材料</li>
                                        <li>建议认真准备，争取一次通过</li>
                                    </ul>
                                </div>
                            </el-collapse-item>
                        </el-collapse>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </main>

        <!-- 面试详情对话框 -->
        <el-dialog title="面试详情" :visible.sync="showDetailsDialog" width="600px">
            <div v-if="selectedInterview">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="面试类型">{{ selectedInterview.type }}</el-descriptions-item>
                    <el-descriptions-item label="招飞单位">{{ selectedInterview.company }}</el-descriptions-item>
                    <el-descriptions-item label="面试时间">{{ selectedInterview.datetime }}</el-descriptions-item>
                    <el-descriptions-item label="面试地点">{{ selectedInterview.location }}</el-descriptions-item>
                    <el-descriptions-item label="面试官">{{ selectedInterview.interviewer }}</el-descriptions-item>
                    <el-descriptions-item label="联系电话">{{ selectedInterview.phone }}</el-descriptions-item>
                </el-descriptions>
                
                <div style="margin-top: 20px;">
                    <h4>面试要求：</h4>
                    <el-alert
                        title="请提前30分钟到达，携带相关证件和材料"
                        type="info"
                        :closable="false">
                    </el-alert>
                </div>
            </div>
        </el-dialog>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    activeTab: 'schedule',
                    activeFaq: ['process'],
                    showDetailsDialog: false,
                    selectedInterview: null,
                    interviews: [
                        {
                            id: 1,
                            type: '初试',
                            company: '中国国际航空',
                            datetime: '2024-04-15 09:00',
                            location: '北京市朝阳区国航大厦',
                            interviewer: '张面试官',
                            phone: '010-12345678',
                            status: 'upcoming'
                        },
                        {
                            id: 2,
                            type: '复试',
                            company: '中国国际航空',
                            datetime: '2024-04-20 14:00',
                            location: '北京市朝阳区国航大厦',
                            interviewer: '李面试官',
                            phone: '010-12345678',
                            status: 'completed'
                        }
                    ],
                    interviewResults: [
                        {
                            id: 1,
                            type: '初试',
                            datetime: '2024-04-15 09:00',
                            interviewer: '张面试官',
                            overall: 'pass',
                            totalScore: 85,
                            scores: [
                                { name: '综合素质', score: 26, maxScore: 30 },
                                { name: '专业素养', score: 22, maxScore: 25 },
                                { name: '英语能力', score: 17, maxScore: 20 },
                                { name: '心理素质', score: 12, maxScore: 15 },
                                { name: '团队协作', score: 8, maxScore: 10 }
                            ],
                            feedback: '考生表现优秀，具备良好的综合素质和专业潜质，英语表达流利，心理素质稳定，建议进入下一轮选拔。'
                        }
                    ]
                }
            },
            methods: {
                handleLogout() {
                    this.$confirm('确认退出登录？', '确认退出', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        logout();
                    });
                },
                getStatusText(status) {
                    const statusMap = {
                        'upcoming': '即将开始',
                        'completed': '已完成',
                        'cancelled': '已取消'
                    };
                    return statusMap[status] || status;
                },
                viewDetails(interview) {
                    this.selectedInterview = interview;
                    this.showDetailsDialog = true;
                },
                confirmAttendance(interview) {
                    this.$confirm('确认参加此次面试？', '确认参加', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'info'
                    }).then(() => {
                        this.$message.success('确认成功！请按时参加面试');
                    });
                },
                requestReschedule(interview) {
                    this.$prompt('请说明申请改期的原因', '申请改期', {
                        confirmButtonText: '提交申请',
                        cancelButtonText: '取消',
                        inputType: 'textarea'
                    }).then(({ value }) => {
                        this.$message.success('改期申请已提交，请等待审核');
                    });
                },
                downloadCertificate(result) {
                    this.$message.success('成绩单下载中...');
                },
                viewImprovement(result) {
                    this.$alert('建议加强英语口语练习，提升专业知识储备，增强心理素质训练。', '改进建议', {
                        confirmButtonText: '知道了'
                    });
                }
            }
        });
    </script>
</body>
</html>
