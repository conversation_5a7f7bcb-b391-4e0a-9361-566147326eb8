<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>体检管理 - 民航招飞管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="../css/common.css">
    <script src="../js/auth.js"></script>
    <style>
        .medical-tabs {
            margin-bottom: 20px;
        }
        
        .appointment-card {
            border: 1px solid #ebeef5;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            transition: box-shadow 0.3s;
        }
        
        .appointment-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .appointment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .appointment-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }
        
        .appointment-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-scheduled {
            background-color: #e1f3d8;
            color: #67c23a;
        }
        
        .status-completed {
            background-color: #f0f9ff;
            color: #409eff;
        }
        
        .status-pending {
            background-color: #fdf6ec;
            color: #e6a23c;
        }
        
        .medical-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f5f7fa;
        }
        
        .medical-item:last-child {
            border-bottom: none;
        }
        
        .medical-name {
            font-weight: 500;
            color: #303133;
        }
        
        .medical-result {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .result-pass {
            color: #67c23a;
        }
        
        .result-fail {
            color: #f56c6c;
        }
        
        .result-pending {
            color: #909399;
        }
        
        .calendar-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
        }
        
        .time-slots {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .time-slot {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .time-slot:hover {
            border-color: #409eff;
            color: #409eff;
        }
        
        .time-slot.selected {
            background-color: #409eff;
            color: white;
            border-color: #409eff;
        }
        
        .time-slot.disabled {
            background-color: #f5f7fa;
            color: #c0c4cc;
            cursor: not-allowed;
        }
        
        .progress-bar {
            margin: 20px 0;
        }
        
        .medical-flow {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .flow-step {
            flex: 1;
            text-align: center;
            position: relative;
        }
        
        .flow-step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 20px;
            right: -50%;
            width: 100%;
            height: 2px;
            background-color: #e4e7ed;
            z-index: 1;
        }
        
        .flow-step.completed::after {
            background-color: #67c23a;
        }
        
        .flow-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e4e7ed;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            position: relative;
            z-index: 2;
        }
        
        .flow-step.completed .flow-icon {
            background-color: #67c23a;
        }
        
        .flow-step.current .flow-icon {
            background-color: #409eff;
        }
        
        .flow-label {
            font-size: 14px;
            color: #606266;
        }
        
        .flow-step.completed .flow-label,
        .flow-step.current .flow-label {
            color: #303133;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    <i class="el-icon-s-promotion"></i>
                    民航招飞系统
                </a>
                
                <nav>
                    <ul class="nav-menu">
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="registration.html">在线报名</a></li>
                        <li><a href="medical.html" class="active">体检管理</a></li>
                        <li><a href="interview.html">面试管理</a></li>
                        <li><a href="status.html">状态查询</a></li>
                    </ul>
                </nav>
                
                <div class="user-info">
                    <span style="color: white;">欢迎，<span class="user-name">用户</span></span>
                    <el-button type="text" style="color: white; margin-left: 15px;" @click="handleLogout">
                        <i class="el-icon-switch-button"></i> 退出
                    </el-button>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="main-content">
            <h1 class="page-title">
                <i class="el-icon-monitor"></i>
                体检管理
            </h1>

            <!-- 体检流程 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">体检流程</h3>
                </div>
                
                <div class="medical-flow">
                    <div class="flow-step completed">
                        <div class="flow-icon">
                            <i class="el-icon-check"></i>
                        </div>
                        <div class="flow-label">预约体检</div>
                    </div>
                    <div class="flow-step completed">
                        <div class="flow-icon">
                            <i class="el-icon-check"></i>
                        </div>
                        <div class="flow-label">初检</div>
                    </div>
                    <div class="flow-step current">
                        <div class="flow-icon">
                            <i class="el-icon-loading"></i>
                        </div>
                        <div class="flow-label">复检</div>
                    </div>
                    <div class="flow-step">
                        <div class="flow-icon">
                            <i class="el-icon-time"></i>
                        </div>
                        <div class="flow-label">终检</div>
                    </div>
                    <div class="flow-step">
                        <div class="flow-icon">
                            <i class="el-icon-document"></i>
                        </div>
                        <div class="flow-label">结果确认</div>
                    </div>
                </div>
            </div>

            <!-- 标签页 -->
            <div class="card">
                <el-tabs v-model="activeTab" class="medical-tabs">
                    <el-tab-pane label="体检预约" name="appointment">
                        <div class="calendar-container">
                            <h3 style="margin-bottom: 20px;">选择体检时间</h3>
                            
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form label-width="100px">
                                        <el-form-item label="体检类型">
                                            <el-select v-model="appointmentForm.type" placeholder="请选择体检类型">
                                                <el-option label="初检" value="initial"></el-option>
                                                <el-option label="复检" value="recheck"></el-option>
                                                <el-option label="终检" value="final"></el-option>
                                            </el-select>
                                        </el-form-item>
                                        
                                        <el-form-item label="体检机构">
                                            <el-select v-model="appointmentForm.hospital" placeholder="请选择体检机构">
                                                <el-option label="民航总医院" value="caac_hospital"></el-option>
                                                <el-option label="北京协和医院" value="peking_union"></el-option>
                                                <el-option label="上海华山医院" value="huashan"></el-option>
                                                <el-option label="广州中山医院" value="zhongshan"></el-option>
                                            </el-select>
                                        </el-form-item>
                                        
                                        <el-form-item label="选择日期">
                                            <el-date-picker
                                                v-model="appointmentForm.date"
                                                type="date"
                                                placeholder="选择日期"
                                                :picker-options="datePickerOptions"
                                                style="width: 100%;">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-form>
                                </el-col>
                                
                                <el-col :span="12">
                                    <h4>可预约时间段</h4>
                                    <div class="time-slots">
                                        <div 
                                            v-for="slot in timeSlots" 
                                            :key="slot.time"
                                            :class="['time-slot', { 
                                                'selected': appointmentForm.time === slot.time,
                                                'disabled': !slot.available 
                                            }]"
                                            @click="selectTimeSlot(slot)">
                                            {{ slot.time }}
                                            <div style="font-size: 12px; color: #909399;">
                                                {{ slot.available ? '可预约' : '已满' }}
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <el-button 
                                        type="primary" 
                                        style="width: 100%; margin-top: 20px;"
                                        :disabled="!appointmentForm.date || !appointmentForm.time"
                                        @click="confirmAppointment">
                                        确认预约
                                    </el-button>
                                </el-col>
                            </el-row>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="体检记录" name="records">
                        <div v-for="record in medicalRecords" :key="record.id" class="appointment-card">
                            <div class="appointment-header">
                                <div class="appointment-title">{{ record.type }} - {{ record.hospital }}</div>
                                <div :class="['appointment-status', `status-${record.status}`]">
                                    {{ getStatusText(record.status) }}
                                </div>
                            </div>
                            
                            <el-row :gutter="20">
                                <el-col :span="8">
                                    <p><strong>体检时间：</strong>{{ record.date }}</p>
                                    <p><strong>体检地点：</strong>{{ record.location }}</p>
                                </el-col>
                                <el-col :span="8">
                                    <p><strong>体检医生：</strong>{{ record.doctor }}</p>
                                    <p><strong>联系电话：</strong>{{ record.phone }}</p>
                                </el-col>
                                <el-col :span="8">
                                    <el-button type="primary" size="small" @click="viewDetails(record)">
                                        查看详情
                                    </el-button>
                                    <el-button v-if="record.status === 'scheduled'" type="warning" size="small" @click="reschedule(record)">
                                        重新预约
                                    </el-button>
                                </el-col>
                            </el-row>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="体检结果" name="results">
                        <div v-for="result in medicalResults" :key="result.id" class="appointment-card">
                            <div class="appointment-header">
                                <div class="appointment-title">{{ result.type }} 体检结果</div>
                                <div :class="['appointment-status', result.overall === 'pass' ? 'status-completed' : 'status-pending']">
                                    {{ result.overall === 'pass' ? '合格' : '待复检' }}
                                </div>
                            </div>
                            
                            <div style="margin-bottom: 15px;">
                                <p><strong>体检时间：</strong>{{ result.date }}</p>
                                <p><strong>体检机构：</strong>{{ result.hospital }}</p>
                            </div>
                            
                            <h4 style="margin-bottom: 15px;">检查项目</h4>
                            <div v-for="item in result.items" :key="item.name" class="medical-item">
                                <span class="medical-name">{{ item.name }}</span>
                                <div class="medical-result">
                                    <span :class="['result-' + item.result]">
                                        {{ item.result === 'pass' ? '合格' : item.result === 'fail' ? '不合格' : '待检' }}
                                    </span>
                                    <el-button v-if="item.report" type="text" size="small" @click="viewReport(item.report)">
                                        查看报告
                                    </el-button>
                                </div>
                            </div>
                            
                            <div style="margin-top: 15px;">
                                <el-button type="primary" size="small" @click="downloadReport(result)">
                                    <i class="el-icon-download"></i> 下载完整报告
                                </el-button>
                                <el-button v-if="result.overall !== 'pass'" type="warning" size="small" @click="scheduleRecheck(result)">
                                    <i class="el-icon-refresh"></i> 申请复检
                                </el-button>
                            </div>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="注意事项" name="guidelines">
                        <div class="card">
                            <h3 style="margin-bottom: 20px;">体检注意事项</h3>
                            
                            <el-collapse v-model="activeGuidelines">
                                <el-collapse-item title="体检前准备" name="before">
                                    <div>
                                        <h4>饮食要求：</h4>
                                        <ul>
                                            <li>体检前一天晚上8点后禁食，体检当天早晨禁食禁水</li>
                                            <li>体检前3天避免饮酒，避免高脂肪、高蛋白饮食</li>
                                            <li>体检前一周停用各种补品、保健品</li>
                                        </ul>
                                        
                                        <h4>作息要求：</h4>
                                        <ul>
                                            <li>体检前3天保证充足睡眠，避免熬夜</li>
                                            <li>体检前避免剧烈运动</li>
                                            <li>保持心情平静，避免紧张焦虑</li>
                                        </ul>
                                    </div>
                                </el-collapse-item>
                                
                                <el-collapse-item title="体检当天" name="during">
                                    <div>
                                        <h4>携带物品：</h4>
                                        <ul>
                                            <li>身份证原件</li>
                                            <li>体检通知书</li>
                                            <li>近期免冠照片2张</li>
                                            <li>既往病历资料（如有）</li>
                                        </ul>
                                        
                                        <h4>注意事项：</h4>
                                        <ul>
                                            <li>提前30分钟到达体检地点</li>
                                            <li>穿着宽松、易脱的衣物</li>
                                            <li>女性避开生理期</li>
                                            <li>配合医生完成各项检查</li>
                                        </ul>
                                    </div>
                                </el-collapse-item>
                                
                                <el-collapse-item title="体检后" name="after">
                                    <div>
                                        <h4>结果查询：</h4>
                                        <ul>
                                            <li>体检结果一般在3-5个工作日内出具</li>
                                            <li>可通过系统查询或电话咨询</li>
                                            <li>如有异常需要复检，请及时预约</li>
                                        </ul>
                                        
                                        <h4>后续安排：</h4>
                                        <ul>
                                            <li>体检合格者等待面试通知</li>
                                            <li>体检不合格者可申请复检（限一次）</li>
                                            <li>保持联系方式畅通</li>
                                        </ul>
                                    </div>
                                </el-collapse-item>
                            </el-collapse>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </main>

        <!-- 体检详情对话框 -->
        <el-dialog title="体检详情" :visible.sync="showDetailsDialog" width="600px">
            <div v-if="selectedRecord">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="体检类型">{{ selectedRecord.type }}</el-descriptions-item>
                    <el-descriptions-item label="体检时间">{{ selectedRecord.date }}</el-descriptions-item>
                    <el-descriptions-item label="体检机构">{{ selectedRecord.hospital }}</el-descriptions-item>
                    <el-descriptions-item label="体检地点">{{ selectedRecord.location }}</el-descriptions-item>
                    <el-descriptions-item label="主检医生">{{ selectedRecord.doctor }}</el-descriptions-item>
                    <el-descriptions-item label="联系电话">{{ selectedRecord.phone }}</el-descriptions-item>
                </el-descriptions>
                
                <div style="margin-top: 20px;">
                    <h4>特别提醒：</h4>
                    <el-alert
                        title="请提前30分钟到达体检地点，携带身份证和体检通知书"
                        type="info"
                        :closable="false">
                    </el-alert>
                </div>
            </div>
        </el-dialog>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    activeTab: 'appointment',
                    activeGuidelines: ['before'],
                    showDetailsDialog: false,
                    selectedRecord: null,
                    appointmentForm: {
                        type: '',
                        hospital: '',
                        date: '',
                        time: ''
                    },
                    datePickerOptions: {
                        disabledDate(time) {
                            return time.getTime() < Date.now() - 8.64e7;
                        }
                    },
                    timeSlots: [
                        { time: '08:00-09:00', available: true },
                        { time: '09:00-10:00', available: false },
                        { time: '10:00-11:00', available: true },
                        { time: '11:00-12:00', available: true },
                        { time: '14:00-15:00', available: true },
                        { time: '15:00-16:00', available: false },
                        { time: '16:00-17:00', available: true }
                    ],
                    medicalRecords: [
                        {
                            id: 1,
                            type: '初检',
                            hospital: '民航总医院',
                            date: '2024-03-15 09:00',
                            location: '北京市朝阳区',
                            doctor: '李医生',
                            phone: '010-12345678',
                            status: 'completed'
                        },
                        {
                            id: 2,
                            type: '复检',
                            hospital: '民航总医院',
                            date: '2024-03-25 14:00',
                            location: '北京市朝阳区',
                            doctor: '王医生',
                            phone: '010-12345678',
                            status: 'scheduled'
                        }
                    ],
                    medicalResults: [
                        {
                            id: 1,
                            type: '初检',
                            date: '2024-03-15',
                            hospital: '民航总医院',
                            overall: 'pass',
                            items: [
                                { name: '视力检查', result: 'pass', report: 'vision_report.pdf' },
                                { name: '听力检查', result: 'pass', report: 'hearing_report.pdf' },
                                { name: '心电图', result: 'pass', report: 'ecg_report.pdf' },
                                { name: '血常规', result: 'pass', report: 'blood_report.pdf' },
                                { name: '胸部X光', result: 'pass', report: 'xray_report.pdf' }
                            ]
                        }
                    ]
                }
            },
            methods: {
                handleLogout() {
                    this.$confirm('确认退出登录？', '确认退出', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        logout();
                    });
                },
                selectTimeSlot(slot) {
                    if (slot.available) {
                        this.appointmentForm.time = slot.time;
                    }
                },
                confirmAppointment() {
                    this.$confirm('确认预约体检？', '确认预约', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'info'
                    }).then(() => {
                        this.$message.success('预约成功！');
                        // 添加到记录中
                        this.medicalRecords.push({
                            id: Date.now(),
                            type: this.appointmentForm.type === 'initial' ? '初检' : 
                                  this.appointmentForm.type === 'recheck' ? '复检' : '终检',
                            hospital: this.getHospitalName(this.appointmentForm.hospital),
                            date: this.appointmentForm.date + ' ' + this.appointmentForm.time.split('-')[0],
                            location: '待确认',
                            doctor: '待分配',
                            phone: '待确认',
                            status: 'scheduled'
                        });
                        // 重置表单
                        this.appointmentForm = {
                            type: '',
                            hospital: '',
                            date: '',
                            time: ''
                        };
                    });
                },
                getHospitalName(value) {
                    const hospitals = {
                        'caac_hospital': '民航总医院',
                        'peking_union': '北京协和医院',
                        'huashan': '上海华山医院',
                        'zhongshan': '广州中山医院'
                    };
                    return hospitals[value] || value;
                },
                getStatusText(status) {
                    const statusMap = {
                        'scheduled': '已预约',
                        'completed': '已完成',
                        'pending': '待处理'
                    };
                    return statusMap[status] || status;
                },
                viewDetails(record) {
                    this.selectedRecord = record;
                    this.showDetailsDialog = true;
                },
                reschedule(record) {
                    this.$message.info('重新预约功能开发中...');
                },
                viewReport(report) {
                    this.$message.info('查看报告：' + report);
                },
                downloadReport(result) {
                    this.$message.success('报告下载中...');
                },
                scheduleRecheck(result) {
                    this.$message.info('申请复检功能开发中...');
                }
            }
        });
    </script>
</body>
</html>
