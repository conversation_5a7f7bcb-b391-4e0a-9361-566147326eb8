<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线报名 - 民航招飞管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="../css/common.css">
    <style>
        .form-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #409eff;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .upload-area {
            border: 2px dashed #d9d9d9;
            border-radius: 6px;
            padding: 20px;
            text-align: center;
            transition: border-color 0.3s;
        }
        
        .upload-area:hover {
            border-color: #409eff;
        }
        
        .progress-steps {
            margin-bottom: 30px;
        }
        
        .step-content {
            min-height: 400px;
        }
        
        .volunteer-item {
            border: 1px solid #ebeef5;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 15px;
            background: #fafafa;
        }
        
        .volunteer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .volunteer-title {
            font-weight: 600;
            color: #303133;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    <i class="el-icon-s-promotion"></i>
                    民航招飞系统
                </a>
                
                <nav>
                    <ul class="nav-menu">
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="registration.html" class="active">在线报名</a></li>
                        <li><a href="medical.html">体检管理</a></li>
                        <li><a href="interview.html">面试管理</a></li>
                        <li><a href="status.html">状态查询</a></li>
                    </ul>
                </nav>
                
                <div class="user-info">
                    <span style="color: white;">欢迎，张三</span>
                    <el-button type="text" style="color: white; margin-left: 15px;">
                        <i class="el-icon-switch-button"></i> 退出
                    </el-button>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="main-content">
            <h1 class="page-title">
                <i class="el-icon-edit-outline"></i>
                在线报名
            </h1>

            <div class="card">
                <!-- 进度步骤 -->
                <div class="progress-steps">
                    <el-steps :active="currentStep" finish-status="success">
                        <el-step title="基本信息" description="填写个人基本信息"></el-step>
                        <el-step title="志愿填报" description="选择报考院校和专业"></el-step>
                        <el-step title="材料上传" description="上传相关证明材料"></el-step>
                        <el-step title="确认提交" description="确认信息并提交申请"></el-step>
                    </el-steps>
                </div>

                <!-- 步骤内容 -->
                <div class="step-content">
                    <!-- 第一步：基本信息 -->
                    <div v-show="currentStep === 0">
                        <div class="form-section">
                            <h3 class="section-title">个人基本信息</h3>
                            <el-form :model="basicForm" label-width="120px">
                                <div class="form-row">
                                    <el-form-item label="姓名" required>
                                        <el-input v-model="basicForm.name" placeholder="请输入真实姓名"></el-input>
                                    </el-form-item>
                                    <el-form-item label="性别" required>
                                        <el-radio-group v-model="basicForm.gender">
                                            <el-radio label="male">男</el-radio>
                                            <el-radio label="female">女</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </div>
                                
                                <div class="form-row">
                                    <el-form-item label="出生日期" required>
                                        <el-date-picker v-model="basicForm.birthDate" type="date" placeholder="选择日期" style="width: 100%;"></el-date-picker>
                                    </el-form-item>
                                    <el-form-item label="身份证号" required>
                                        <el-input v-model="basicForm.idCard" placeholder="请输入身份证号"></el-input>
                                    </el-form-item>
                                </div>
                                
                                <div class="form-row">
                                    <el-form-item label="手机号码" required>
                                        <el-input v-model="basicForm.phone" placeholder="请输入手机号码"></el-input>
                                    </el-form-item>
                                    <el-form-item label="邮箱地址" required>
                                        <el-input v-model="basicForm.email" placeholder="请输入邮箱地址"></el-input>
                                    </el-form-item>
                                </div>
                                
                                <div class="form-row">
                                    <el-form-item label="民族">
                                        <el-select v-model="basicForm.ethnicity" placeholder="请选择民族">
                                            <el-option label="汉族" value="汉族"></el-option>
                                            <el-option label="蒙古族" value="蒙古族"></el-option>
                                            <el-option label="回族" value="回族"></el-option>
                                            <el-option label="藏族" value="藏族"></el-option>
                                            <el-option label="其他" value="其他"></el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="政治面貌">
                                        <el-select v-model="basicForm.politicalStatus" placeholder="请选择政治面貌">
                                            <el-option label="群众" value="群众"></el-option>
                                            <el-option label="共青团员" value="共青团员"></el-option>
                                            <el-option label="中共党员" value="中共党员"></el-option>
                                            <el-option label="其他" value="其他"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>
                            </el-form>
                        </div>

                        <div class="form-section">
                            <h3 class="section-title">联系信息</h3>
                            <el-form :model="basicForm" label-width="120px">
                                <div class="form-row">
                                    <el-form-item label="户籍所在地" required>
                                        <el-cascader v-model="basicForm.hometown" :options="regionOptions" placeholder="请选择户籍所在地"></el-cascader>
                                    </el-form-item>
                                    <el-form-item label="现居住地址" required>
                                        <el-input v-model="basicForm.address" placeholder="请输入详细地址"></el-input>
                                    </el-form-item>
                                </div>
                                
                                <div class="form-row">
                                    <el-form-item label="紧急联系人">
                                        <el-input v-model="basicForm.emergencyContact" placeholder="请输入紧急联系人姓名"></el-input>
                                    </el-form-item>
                                    <el-form-item label="联系人电话">
                                        <el-input v-model="basicForm.emergencyPhone" placeholder="请输入联系人电话"></el-input>
                                    </el-form-item>
                                </div>
                            </el-form>
                        </div>

                        <div class="form-section">
                            <h3 class="section-title">教育背景</h3>
                            <el-form :model="basicForm" label-width="120px">
                                <div class="form-row">
                                    <el-form-item label="毕业学校" required>
                                        <el-input v-model="basicForm.school" placeholder="请输入毕业学校名称"></el-input>
                                    </el-form-item>
                                    <el-form-item label="学历层次" required>
                                        <el-select v-model="basicForm.education" placeholder="请选择学历层次">
                                            <el-option label="高中" value="高中"></el-option>
                                            <el-option label="中专" value="中专"></el-option>
                                            <el-option label="大专" value="大专"></el-option>
                                            <el-option label="本科" value="本科"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>
                                
                                <div class="form-row">
                                    <el-form-item label="毕业时间">
                                        <el-date-picker v-model="basicForm.graduationDate" type="date" placeholder="选择毕业时间" style="width: 100%;"></el-date-picker>
                                    </el-form-item>
                                    <el-form-item label="专业">
                                        <el-input v-model="basicForm.major" placeholder="请输入专业名称"></el-input>
                                    </el-form-item>
                                </div>
                            </el-form>
                        </div>
                    </div>

                    <!-- 第二步：志愿填报 -->
                    <div v-show="currentStep === 1">
                        <div class="form-section">
                            <h3 class="section-title">志愿填报</h3>
                            <p style="color: #909399; margin-bottom: 20px;">
                                您最多可以填报3个志愿，请按照优先级顺序填写。
                            </p>
                            
                            <div v-for="(volunteer, index) in volunteers" :key="index" class="volunteer-item">
                                <div class="volunteer-header">
                                    <span class="volunteer-title">第{{ index + 1 }}志愿</span>
                                    <el-button v-if="index > 0" type="text" @click="removeVolunteer(index)">
                                        <i class="el-icon-delete"></i> 删除
                                    </el-button>
                                </div>
                                
                                <el-form :model="volunteer" label-width="120px">
                                    <div class="form-row">
                                        <el-form-item label="招飞单位" required>
                                            <el-select v-model="volunteer.company" placeholder="请选择招飞单位">
                                                <el-option label="中国国际航空股份有限公司" value="国航"></el-option>
                                                <el-option label="中国东方航空股份有限公司" value="东航"></el-option>
                                                <el-option label="中国南方航空股份有限公司" value="南航"></el-option>
                                                <el-option label="海南航空股份有限公司" value="海航"></el-option>
                                                <el-option label="春秋航空股份有限公司" value="春秋航空"></el-option>
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="培养院校" required>
                                            <el-select v-model="volunteer.school" placeholder="请选择培养院校">
                                                <el-option label="中国民航大学" value="中国民航大学"></el-option>
                                                <el-option label="中国民用航空飞行学院" value="中国民用航空飞行学院"></el-option>
                                                <el-option label="南京航空航天大学" value="南京航空航天大学"></el-option>
                                                <el-option label="北京航空航天大学" value="北京航空航天大学"></el-option>
                                                <el-option label="沈阳航空航天大学" value="沈阳航空航天大学"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </div>
                                    
                                    <div class="form-row">
                                        <el-form-item label="专业方向" required>
                                            <el-select v-model="volunteer.major" placeholder="请选择专业方向">
                                                <el-option label="飞行技术" value="飞行技术"></el-option>
                                                <el-option label="交通运输（飞行技术方向）" value="交通运输"></el-option>
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="培养模式">
                                            <el-select v-model="volunteer.mode" placeholder="请选择培养模式">
                                                <el-option label="养成生" value="养成生"></el-option>
                                                <el-option label="大改驾" value="大改驾"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </div>
                                </el-form>
                            </div>
                            
                            <el-button v-if="volunteers.length < 3" type="dashed" @click="addVolunteer" style="width: 100%; margin-top: 15px;">
                                <i class="el-icon-plus"></i> 添加志愿
                            </el-button>
                        </div>
                    </div>

                    <!-- 第三步：材料上传 -->
                    <div v-show="currentStep === 2">
                        <div class="form-section">
                            <h3 class="section-title">材料上传</h3>
                            <p style="color: #909399; margin-bottom: 20px;">
                                请上传以下必需材料，支持JPG、PNG、PDF格式，单个文件不超过5MB。
                            </p>
                            
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="身份证正面">
                                        <el-upload
                                            class="upload-demo"
                                            drag
                                            action="#"
                                            :auto-upload="false"
                                            :show-file-list="true">
                                            <i class="el-icon-upload"></i>
                                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                                
                                <el-col :span="12">
                                    <el-form-item label="身份证反面">
                                        <el-upload
                                            class="upload-demo"
                                            drag
                                            action="#"
                                            :auto-upload="false"
                                            :show-file-list="true">
                                            <i class="el-icon-upload"></i>
                                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="学历证明">
                                        <el-upload
                                            class="upload-demo"
                                            drag
                                            action="#"
                                            :auto-upload="false"
                                            :show-file-list="true">
                                            <i class="el-icon-upload"></i>
                                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                                
                                <el-col :span="12">
                                    <el-form-item label="体检报告">
                                        <el-upload
                                            class="upload-demo"
                                            drag
                                            action="#"
                                            :auto-upload="false"
                                            :show-file-list="true">
                                            <i class="el-icon-upload"></i>
                                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="个人照片">
                                        <el-upload
                                            class="upload-demo"
                                            drag
                                            action="#"
                                            :auto-upload="false"
                                            :show-file-list="true">
                                            <i class="el-icon-upload"></i>
                                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                                
                                <el-col :span="12">
                                    <el-form-item label="其他材料">
                                        <el-upload
                                            class="upload-demo"
                                            drag
                                            action="#"
                                            :auto-upload="false"
                                            :show-file-list="true"
                                            multiple>
                                            <i class="el-icon-upload"></i>
                                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                        </el-upload>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <!-- 第四步：确认提交 -->
                    <div v-show="currentStep === 3">
                        <div class="form-section">
                            <h3 class="section-title">信息确认</h3>
                            <el-alert
                                title="请仔细核对以下信息，确认无误后提交申请"
                                type="info"
                                :closable="false"
                                style="margin-bottom: 20px;">
                            </el-alert>
                            
                            <!-- 基本信息确认 -->
                            <el-card style="margin-bottom: 20px;">
                                <div slot="header">
                                    <span>基本信息</span>
                                </div>
                                <el-descriptions :column="2" border>
                                    <el-descriptions-item label="姓名">{{ basicForm.name }}</el-descriptions-item>
                                    <el-descriptions-item label="性别">{{ basicForm.gender === 'male' ? '男' : '女' }}</el-descriptions-item>
                                    <el-descriptions-item label="身份证号">{{ basicForm.idCard }}</el-descriptions-item>
                                    <el-descriptions-item label="手机号码">{{ basicForm.phone }}</el-descriptions-item>
                                    <el-descriptions-item label="邮箱地址">{{ basicForm.email }}</el-descriptions-item>
                                    <el-descriptions-item label="毕业学校">{{ basicForm.school }}</el-descriptions-item>
                                </el-descriptions>
                            </el-card>
                            
                            <!-- 志愿信息确认 -->
                            <el-card style="margin-bottom: 20px;">
                                <div slot="header">
                                    <span>志愿信息</span>
                                </div>
                                <div v-for="(volunteer, index) in volunteers" :key="index" style="margin-bottom: 15px;">
                                    <h4>第{{ index + 1 }}志愿</h4>
                                    <el-descriptions :column="2" border size="small">
                                        <el-descriptions-item label="招飞单位">{{ volunteer.company }}</el-descriptions-item>
                                        <el-descriptions-item label="培养院校">{{ volunteer.school }}</el-descriptions-item>
                                        <el-descriptions-item label="专业方向">{{ volunteer.major }}</el-descriptions-item>
                                        <el-descriptions-item label="培养模式">{{ volunteer.mode }}</el-descriptions-item>
                                    </el-descriptions>
                                </div>
                            </el-card>
                            
                            <!-- 承诺书 -->
                            <el-card>
                                <div slot="header">
                                    <span>申请承诺</span>
                                </div>
                                <el-checkbox v-model="agreement">
                                    我承诺所填写的信息真实有效，如有虚假信息愿承担相应责任。我已阅读并同意
                                    <el-button type="text">《招飞申请条款》</el-button>
                                    和
                                    <el-button type="text">《隐私政策》</el-button>
                                </el-checkbox>
                            </el-card>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="text-center" style="margin-top: 30px;">
                    <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
                    <el-button v-if="currentStep < 3" type="primary" @click="nextStep">下一步</el-button>
                    <el-button v-if="currentStep === 3" type="success" @click="submitApplication" :disabled="!agreement">
                        提交申请
                    </el-button>
                </div>
            </div>
        </main>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    currentStep: 0,
                    agreement: false,
                    basicForm: {
                        name: '',
                        gender: '',
                        birthDate: '',
                        idCard: '',
                        phone: '',
                        email: '',
                        ethnicity: '',
                        politicalStatus: '',
                        hometown: [],
                        address: '',
                        emergencyContact: '',
                        emergencyPhone: '',
                        school: '',
                        education: '',
                        graduationDate: '',
                        major: ''
                    },
                    volunteers: [
                        {
                            company: '',
                            school: '',
                            major: '',
                            mode: ''
                        }
                    ],
                    regionOptions: [
                        {
                            value: 'beijing',
                            label: '北京市',
                            children: [
                                { value: 'dongcheng', label: '东城区' },
                                { value: 'xicheng', label: '西城区' }
                            ]
                        },
                        {
                            value: 'shanghai',
                            label: '上海市',
                            children: [
                                { value: 'huangpu', label: '黄浦区' },
                                { value: 'xuhui', label: '徐汇区' }
                            ]
                        }
                    ]
                }
            },
            methods: {
                nextStep() {
                    if (this.currentStep < 3) {
                        this.currentStep++;
                    }
                },
                prevStep() {
                    if (this.currentStep > 0) {
                        this.currentStep--;
                    }
                },
                addVolunteer() {
                    if (this.volunteers.length < 3) {
                        this.volunteers.push({
                            company: '',
                            school: '',
                            major: '',
                            mode: ''
                        });
                    }
                },
                removeVolunteer(index) {
                    this.volunteers.splice(index, 1);
                },
                submitApplication() {
                    this.$confirm('确认提交申请？提交后将无法修改。', '确认提交', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // 模拟提交
                        this.$message.success('申请提交成功！');
                        setTimeout(() => {
                            window.location.href = 'status.html';
                        }, 2000);
                    });
                }
            }
        });
    </script>
</body>
</html>
