// 认证相关的通用函数

// 检查用户是否已登录
function checkAuth() {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    const currentUser = localStorage.getItem('currentUser');
    
    if (isLoggedIn !== 'true' || !currentUser) {
        // 未登录，跳转到首页并提示登录
        alert('请先登录后再访问该功能');
        window.location.href = '../index.html';
        return false;
    }
    
    return JSON.parse(currentUser);
}

// 检查管理员权限
function checkAdminAuth() {
    const user = checkAuth();
    if (!user) return false;
    
    if (user.userType !== 'admin') {
        alert('您没有权限访问该页面');
        window.location.href = '../index.html';
        return false;
    }
    
    return user;
}

// 退出登录
function logout() {
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentUser');
    window.location.href = '../index.html';
}

// 获取当前用户信息
function getCurrentUser() {
    const currentUser = localStorage.getItem('currentUser');
    return currentUser ? JSON.parse(currentUser) : null;
}

// 更新用户显示信息
function updateUserDisplay() {
    const user = getCurrentUser();
    if (user) {
        // 更新页面中的用户信息显示
        const userNameElements = document.querySelectorAll('.user-name');
        userNameElements.forEach(element => {
            element.textContent = user.username || '用户';
        });
    }
}

// 页面加载时执行认证检查
document.addEventListener('DOMContentLoaded', function() {
    // 如果当前页面不是首页，则检查登录状态
    if (!window.location.pathname.includes('index.html') && 
        !window.location.pathname.endsWith('/')) {
        checkAuth();
        updateUserDisplay();
    }
});
