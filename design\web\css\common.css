/* 招飞系统通用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
    background-color: #f5f7fa;
    color: #303133;
    line-height: 1.6;
}

/* 主容器 */
.main-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部导航 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 60px;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    color: white;
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.nav-menu a:hover,
.nav-menu a.active {
    background-color: rgba(255, 255, 255, 0.2);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 主内容区 */
.main-content {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
}

/* 页面标题 */
.page-title {
    font-size: 28px;
    color: #303133;
    margin-bottom: 20px;
    font-weight: 500;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 24px;
    margin-bottom: 20px;
}

.card-header {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 16px;
    margin-bottom: 20px;
}

.card-title {
    font-size: 18px;
    font-weight: 500;
    color: #303133;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #606266;
}

.form-input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-input:focus {
    outline: none;
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.form-select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
}

.form-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    resize: vertical;
    min-height: 100px;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s;
    font-weight: 500;
}

.btn-primary {
    background-color: #409eff;
    color: white;
}

.btn-primary:hover {
    background-color: #66b1ff;
}

.btn-success {
    background-color: #67c23a;
    color: white;
}

.btn-success:hover {
    background-color: #85ce61;
}

.btn-warning {
    background-color: #e6a23c;
    color: white;
}

.btn-warning:hover {
    background-color: #ebb563;
}

.btn-danger {
    background-color: #f56c6c;
    color: white;
}

.btn-danger:hover {
    background-color: #f78989;
}

.btn-info {
    background-color: #909399;
    color: white;
}

.btn-info:hover {
    background-color: #a6a9ad;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.table th,
.table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ebeef5;
}

.table th {
    background-color: #fafafa;
    font-weight: 500;
    color: #909399;
}

.table tr:hover {
    background-color: #f5f7fa;
}

/* 状态标签 */
.status-tag {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-pending {
    background-color: #fdf6ec;
    color: #e6a23c;
}

.status-approved {
    background-color: #f0f9ff;
    color: #409eff;
}

.status-passed {
    background-color: #f0f9ff;
    color: #67c23a;
}

.status-failed {
    background-color: #fef0f0;
    color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        height: auto;
        padding: 15px 20px;
    }
    
    .nav-menu {
        margin-top: 15px;
        gap: 15px;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .card {
        padding: 16px;
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-20 { margin-bottom: 20px; }
.mt-20 { margin-top: 20px; }
.hidden { display: none; }
.flex { display: flex; }
.flex-between { justify-content: space-between; }
.flex-center { justify-content: center; }
.align-center { align-items: center; }
