<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>民航招飞管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="css/common.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .hero-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .hero-subtitle {
            font-size: 20px;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 60px;
        }
        
        .feature-card {
            background: white;
            border-radius: 12px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 36px;
            color: white;
        }
        
        .feature-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #303133;
        }
        
        .feature-desc {
            color: #606266;
            line-height: 1.6;
        }
        
        .stats-section {
            background: #f8f9fa;
            padding: 60px 0;
            margin-top: 60px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }
        
        .stat-item {
            color: #303133;
        }
        
        .stat-number {
            font-size: 48px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 16px;
            color: #606266;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <a href="index.html" class="logo">
                    <i class="el-icon-s-promotion"></i>
                    民航招飞系统
                </a>
                
                <nav>
                    <ul class="nav-menu">
                        <li><a href="index.html" class="active">首页</a></li>
                        <li><a href="#" @click="checkLoginAndNavigate('pages/registration.html')">在线报名</a></li>
                        <li><a href="#" @click="checkLoginAndNavigate('pages/medical.html')">体检管理</a></li>
                        <li><a href="#" @click="checkLoginAndNavigate('pages/interview.html')">面试管理</a></li>
                        <li><a href="#" @click="checkLoginAndNavigate('pages/status.html')">状态查询</a></li>
                    </ul>
                </nav>
                
                <div class="user-info">
                    <el-button type="text" style="color: white;" @click="showLogin = true">
                        <i class="el-icon-user"></i> 登录
                    </el-button>
                    <el-button type="text" style="color: white;" @click="showRegister = true">
                        <i class="el-icon-plus"></i> 注册
                    </el-button>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main>
            <!-- 英雄区域 -->
            <section class="hero-section">
                <div class="main-content">
                    <h1 class="hero-title">民航招飞管理系统</h1>
                    <p class="hero-subtitle">专业、高效、便民的招飞服务平台</p>
                    <el-button type="primary" size="large" @click="showRegister = true">
                        立即报名
                    </el-button>
                    <el-button type="info" size="large" plain style="margin-left: 20px;">
                        了解更多
                    </el-button>
                </div>
            </section>

            <!-- 功能特色 -->
            <section class="main-content">
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="el-icon-edit-outline"></i>
                        </div>
                        <h3 class="feature-title">在线报名</h3>
                        <p class="feature-desc">便捷的在线报名流程，支持多种志愿填报，实时状态跟踪</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="el-icon-monitor"></i>
                        </div>
                        <h3 class="feature-title">体检管理</h3>
                        <p class="feature-desc">智能体检预约系统，全程跟踪体检流程，及时查询结果</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="el-icon-chat-dot-round"></i>
                        </div>
                        <h3 class="feature-title">面试安排</h3>
                        <p class="feature-desc">灵活的面试安排系统，专业评分标准，公正透明</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="el-icon-data-analysis"></i>
                        </div>
                        <h3 class="feature-title">数据统计</h3>
                        <p class="feature-desc">全面的数据分析报告，助力招飞决策优化</p>
                    </div>
                </div>
            </section>

            <!-- 统计数据 -->
            <section class="stats-section">
                <div class="main-content">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">15,000+</div>
                            <div class="stat-label">累计报名人数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">98%</div>
                            <div class="stat-label">系统可用性</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">24/7</div>
                            <div class="stat-label">全天候服务</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">合作院校</div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 登录对话框 -->
        <el-dialog title="用户登录" :visible.sync="showLogin" width="400px">
            <el-form :model="loginForm" label-width="80px">
                <el-form-item label="用户名">
                    <el-input v-model="loginForm.username" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item label="密码">
                    <el-input v-model="loginForm.password" type="password" placeholder="请输入密码"></el-input>
                </el-form-item>
                <el-form-item label="用户类型">
                    <el-select v-model="loginForm.userType" placeholder="请选择用户类型">
                        <el-option label="考生" value="student"></el-option>
                        <el-option label="管理员" value="admin"></el-option>
                        <el-option label="体检医生" value="doctor"></el-option>
                        <el-option label="面试官" value="interviewer"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="showLogin = false">取消</el-button>
                <el-button type="primary" @click="handleLogin">登录</el-button>
            </div>
        </el-dialog>

        <!-- 注册对话框 -->
        <el-dialog title="用户注册" :visible.sync="showRegister" width="500px">
            <el-form :model="registerForm" label-width="100px">
                <el-form-item label="姓名">
                    <el-input v-model="registerForm.name" placeholder="请输入真实姓名"></el-input>
                </el-form-item>
                <el-form-item label="身份证号">
                    <el-input v-model="registerForm.idCard" placeholder="请输入身份证号"></el-input>
                </el-form-item>
                <el-form-item label="手机号">
                    <el-input v-model="registerForm.phone" placeholder="请输入手机号"></el-input>
                </el-form-item>
                <el-form-item label="邮箱">
                    <el-input v-model="registerForm.email" placeholder="请输入邮箱"></el-input>
                </el-form-item>
                <el-form-item label="密码">
                    <el-input v-model="registerForm.password" type="password" placeholder="请输入密码"></el-input>
                </el-form-item>
                <el-form-item label="确认密码">
                    <el-input v-model="registerForm.confirmPassword" type="password" placeholder="请确认密码"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="showRegister = false">取消</el-button>
                <el-button type="primary" @click="handleRegister">注册</el-button>
            </div>
        </el-dialog>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    showLogin: false,
                    showRegister: false,
                    isLoggedIn: false,
                    currentUser: null,
                    loginForm: {
                        username: '',
                        password: '',
                        userType: ''
                    },
                    registerForm: {
                        name: '',
                        idCard: '',
                        phone: '',
                        email: '',
                        password: '',
                        confirmPassword: ''
                    }
                }
            },
            methods: {
                checkLoginAndNavigate(url) {
                    if (!this.isLoggedIn) {
                        this.$message.warning('请先登录后再访问该功能');
                        this.showLogin = true;
                        return;
                    }
                    window.location.href = url;
                },
                handleLogin() {
                    // 简单验证
                    if (!this.loginForm.username || !this.loginForm.password || !this.loginForm.userType) {
                        this.$message.error('请填写完整的登录信息');
                        return;
                    }

                    // 模拟登录逻辑
                    this.isLoggedIn = true;
                    this.currentUser = {
                        username: this.loginForm.username,
                        userType: this.loginForm.userType
                    };

                    // 保存登录状态到localStorage
                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('currentUser', JSON.stringify(this.currentUser));

                    this.$message.success('登录成功！');
                    this.showLogin = false;

                    // 根据用户类型跳转到不同页面
                    switch(this.loginForm.userType) {
                        case 'admin':
                            window.location.href = 'pages/admin-dashboard.html';
                            break;
                        case 'student':
                            window.location.href = 'pages/student-dashboard.html';
                            break;
                        default:
                            window.location.href = 'pages/student-dashboard.html';
                    }
                },
                handleRegister() {
                    // 验证必填字段
                    if (!this.registerForm.name || !this.registerForm.idCard ||
                        !this.registerForm.phone || !this.registerForm.email ||
                        !this.registerForm.password || !this.registerForm.confirmPassword) {
                        this.$message.error('请填写完整的注册信息');
                        return;
                    }

                    if (this.registerForm.password !== this.registerForm.confirmPassword) {
                        this.$message.error('两次输入的密码不一致！');
                        return;
                    }

                    // 模拟注册逻辑
                    this.$message.success('注册成功！请登录');
                    this.showRegister = false;
                    this.showLogin = true;

                    // 清空注册表单
                    this.registerForm = {
                        name: '',
                        idCard: '',
                        phone: '',
                        email: '',
                        password: '',
                        confirmPassword: ''
                    };
                }
            },
            mounted() {
                // 检查是否已登录
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                const currentUser = localStorage.getItem('currentUser');

                if (isLoggedIn === 'true' && currentUser) {
                    this.isLoggedIn = true;
                    this.currentUser = JSON.parse(currentUser);
                }
            }
        });
    </script>
</body>
</html>
